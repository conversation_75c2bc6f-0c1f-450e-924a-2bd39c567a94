{block content}
	{snippet content}
		<div class="row-main">
			{embed $templates.'/part/box/header.latte', class: 'u-mb-md', object: $object, templates: $templates}
				{block extra}
					{php $btns = [(object) array('link' => '#positions', 'lang' => 'open_positions')]}
					{include $templates.'/part/box/top.latte', btns: $btns, items: $object->cf->header_usp??->items ?? []}
				{/block}
			{/embed}
			{include $templates.'/part/box/join.latte'}
			{include $templates.'/part/crossroad/positions.latte'}
			{control customContentRenderer}
		</div>
	{/snippet}
{/block}
