{block content}
	{snippet content}
		<div class="row-main">
			<div class="u-maw-11-12 u-mx-auto u-mb-md u-mb-2xl@md">
				{php $formSuccess = false}

				{if $formSuccess}
					{embed $templates.'/part/box/header.latte', name: $translator->translate('title_paid_courses_questionare_sent'), annotation: $translator->translate('annot_paid_courses_questionare_sent'), templates: $templates, object: $object}
                        {block side}
							{include $templates.'/part/box/help.latte', class: false}
                        {/block}
                    {/embed}
				{else}
					{include $templates.'/part/box/header.latte', class: 'tw-mb-[1.6rem]', name: ($object->nameHeading ?? false) . ' Pilot dronu - rekvalifikační kurz'}

					<div class="grid">
						<div class="grid__cell size--8-12@lg">
							<form action="?" class="tw-rounded-md md:tw-rounded-xl tw-border-solid tw-border-[0.1rem] tw-border-tile-light tw-p-[2rem] md:tw-p-[5.2rem]">
								<div class="u-maw-4-12 u-mx-auto u-mb-last-0">
									<p>
										TODO: form fieds
									</p>
									<p class="tw-text-center tw-mb-[1.6rem]">
										<button type="submit" class="btn btn--xl btn--secondary">
											<span class="btn__text">
												<span class="btn__inner">
													{_"btn_submit"}
													{('arrow-right-thin')|icon, 'btn__icon'}
												</span>
											</span>
										</button>
									</p>
									<p class="tw-mb-0 tw-text-[1.4rem] tw-text-center">{_"agree_paid_courses_form"}</p>
								</div>
							</form>
						</div>
						<div class="grid__cell size--4-12@lg">
							{include $templates.'/part/box/help.latte', class: false}
						</div>
					</div>
				{/if}
			</div>
		</div>
	{/snippet}
{/block}
