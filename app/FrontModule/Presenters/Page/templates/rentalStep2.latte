{block content}
	{snippet content}
		{php $isEmpty = false}

		<div class="row-main">
			<div class="u-maw-11-12 u-mx-auto u-mb-md u-mb-2xl@md">
				{snippetArea breadcrumbArea}
					{control breadcrumb, [class: 'u-pt-sm u-pt-md@md tw-mb-[1.2rem] md:tw-mb-[2rem]']}
				{/snippetArea}
				{include $templates.'/part/box/rental-steps.latte', class: 'tw-mb-[2.4rem] lg:tw-mb-[4rem]', currentStep: 1}
				<h1 class="h2 u-mt-0 tw-mb-[1.2rem]">{$object->nameHeading ? $object->nameHeading : $object->name}</h1>

				<div class="tw-flex tw-flex-col xl:tw-flex-row tw-gap-[1.6rem_5.2rem]">
					{* <PERSON><PERSON> sloupec *}
					<div class="tw-flex-1 u-mb-last-0">
						<p class="message message--md message--warning tw-mb-[2rem]">
							<span class="message__emoji">👌</span>
							<span class="message__content">
								{_"rental_step2_msg"|noescape}
							</span>
						</p>
						<div class="md:tw-rounded-xl md:tw-border-solid md:tw-border-[0.1rem] md:tw-border-tile-light md:tw-p-[5rem_8rem] u-mb-last-0 tw-mb-[2rem]">
							<h2 class="h4 tw-mb-[1.2rem] md:tw-mb-[2rem]">{_"rental_step2_takeover_title"}</h2>
							<div class="u-mb-last-0 tw-mb-[1.2rem] md:tw-mb-[2rem]">
								<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
									TODO: inps
								</div>
								<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
									TODO: inp
								</div>
								<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
									TODO: inp
								</div>
							</div>
							<p class="tw-text-help tw-text-[1.3rem] tw-mb-[1.6rem] md:tw-mb-[3.2rem]">
								{_"rental_step2_takeover_info"}
							</p>
							<div class="tw-mb-[1.6rem] md:tw-mb-[3.2rem]">
								<div class="grid grid--x-md grid--y-sm">
									<div class="grid__cell size--6-12@md u-mb-last-0">
										<h2 class="h4 tw-mb-[1.2rem] md:tw-mb-[2rem]">{_"rental_step2_from_title"}</h2>
										<p>
											TODO: inp
										</p>
										<p class="tw-text-help tw-text-[1.3rem] tw-mb-[1.2rem]">{_"rental_step2_date_info"}</p>
										<p>
											TODO: radios
										</p>
									</div>
									<div class="grid__cell size--6-12@md u-mb-last-0">
										<h2 class="h4 tw-mb-[1.2rem] md:tw-mb-[2rem]">{_"rental_step2_to_title"}</h2>
										<p>
											TODO: inp
										</p>
										<p class="tw-text-help tw-text-[1.3rem] tw-mb-[1.2rem]">{_"rental_step2_date_info"}</p>
										<p>
											TODO: radios
										</p>
									</div>
								</div>
							</div>
							<div class="tw-rounded-xl tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[2rem_3.6rem_3.2rem] tw-mb-[3.2rem]">
								TODO: calendar
							</div>
							<p class="tw-flex tw-flex-wrap tw-justify-center tw-text-[1.4rem] tw-gap-[0.8rem_3.2rem] tw-mb-0">
								<span class="tw-flex tw-gap-[1.2rem]"><span class="tw-rounded-sm tw-bg-status-valid-light tw-h-[2rem] tw-aspect-square tw-flex tw-justify-center tw-items-center tw-text-[1.1rem] tw-border-[0.2rem] tw-border-solid tw-border-status-valid">#</span>Dnes</span>
								<span class="tw-flex tw-gap-[1.2rem]"><span class="tw-rounded-sm tw-bg-status-invalid-light tw-text-status-invalid tw-h-[2rem] tw-aspect-square tw-flex tw-justify-center tw-items-center tw-text-[1.1rem]">#</span>Nedostupné</span>
								<span class="tw-flex tw-gap-[1.2rem]"><span class="tw-rounded-sm tw-bg-status-valid-light tw-h-[2rem] tw-aspect-square tw-flex tw-justify-center tw-items-center tw-text-[1.1rem]">#</span>Dostupné</span>
								<span class="tw-flex tw-gap-[1.2rem]"><span class="tw-rounded-sm tw-bg-gradient-to-br tw-from-status-valid-light tw-to-status-invalid-light tw-from-[50%] tw-to-[50.01%] tw-h-[2rem] tw-aspect-square tw-flex tw-justify-center tw-items-center tw-text-[1.1rem]">#</span>Půlden</span>
								<span class="tw-flex tw-gap-[1.2rem]"><span class="tw-rounded-sm tw-bg-status-valid tw-text-white tw-font-bold tw-h-[2rem] tw-aspect-square tw-flex tw-justify-center tw-items-center tw-text-[1.1rem]">#</span>Vybrané</span>
							</p>
						</div>

						<p n:if="isset($pages->contact)" class="c-types__item link-mask tw-mb-[1.6rem] md:tw-mb-[2rem]">
							<span>
								<b class="c-types__name tw-text-text">{_"rental_cta_step2_title"}</b>
								{_"rental_cta_step2_annot"}
							</span>
							<a href="{plink $pages->contact}" class="c-types__btn btn btn--micro link-mask__link">
								<span class="btn__text">
									<span class="btn__inner">
										{_"rental_cta_btn"}
										{('angle-right')|icon, 'btn__icon'}
									</span>
								</span>
							</a>
						</p>
					</div>

					{* Pravý sloupec *}
					<div class="tw-flex-[0_0_auto] xl:tw-w-[40.8rem]">
						{include $templates.'/part/box/rental-summary.latte', isEmpty: $isEmpty}
						{include $templates.'/part/box/help.latte'}
					</div>
				</div>
			</div>
			<hr class="u-mb-md u-mb-2xl@md">
			{control customContentRenderer}
		</div>
	{/snippet}
{/block}
