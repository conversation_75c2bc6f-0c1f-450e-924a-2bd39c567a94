{block content}
	{snippet content}
		{php $isEmpty = false}

		<div class="row-main">
			<div class="u-maw-11-12 u-mx-auto u-mb-md u-mb-2xl@md">
				{snippetArea breadcrumbArea}
					{control breadcrumb, [class: 'u-pt-sm u-pt-md@md tw-mb-[1.2rem] md:tw-mb-[2rem]']}
				{/snippetArea}
				{include $templates.'/part/box/rental-steps.latte', class: 'tw-mb-[2.4rem] lg:tw-mb-[4rem]', currentStep: 1}
				<h1 class="h2 u-mt-0 tw-mb-[1.2rem]">{$object->nameHeading ? $object->nameHeading : $object->name}</h1>

				<div class="tw-flex tw-flex-col xl:tw-flex-row tw-gap-[1.6rem_5.2rem]">
					{* <PERSON><PERSON> sloupec *}
					<div class="tw-flex-1 u-mb-last-0">
						<p n:if="$object->annotation" class="tw-mb-[1.6rem] md:tw-mb-[2rem]">
							{$object->annotation|texy|noescape}
						</p>
						<div class="md:tw-rounded-xl md:tw-border-solid md:tw-border-[0.1rem] md:tw-border-tile-light md:tw-p-[5rem_8rem] u-mb-last-0 tw-mb-[2rem]">
							<h2 class="h4 tw-mb-[1.2rem] md:tw-mb-[2rem]">{_"rental_step3_insurance_title"}</h2>
							<div class="u-mb-last-0 tw-mb-[1.2rem] md:tw-mb-[2rem]">
								<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
									TODO: inps
								</div>
								<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
									TODO: inp
								</div>
								<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
									TODO: inp
								</div>
							</div>
							<p class="tw-text-help tw-text-[1.3rem] tw-mb-[1.6rem] md:tw-mb-[3.2rem]">
								{_"rental_step2_insurance_info"}
							</p>
						</div>
						<p n:if="isset($pages->contact)" class="c-types__item link-mask tw-mb-[1.6rem] md:tw-mb-[2rem]">
							<span>
								<b class="c-types__name tw-text-text">{_"rental_cta_step3_title"}</b>
								{_"rental_cta_step3_annot"}
							</span>
							<a href="{plink $pages->contact}" class="c-types__btn btn btn--micro link-mask__link">
								<span class="btn__text">
									<span class="btn__inner">
										{_"rental_cta_btn"}
										{('angle-right')|icon, 'btn__icon'}
									</span>
								</span>
							</a>
						</p>
					</div>

					{* Pravý sloupec *}
					<div class="tw-flex-[0_0_auto] xl:tw-w-[40.8rem]">
						{include $templates.'/part/box/rental-summary.latte', isEmpty: $isEmpty}
						{include $templates.'/part/box/help.latte'}
					</div>
				</div>
			</div>
			<hr class="u-mb-md u-mb-2xl@md">
			{control customContentRenderer}
		</div>
	{/snippet}
{/block}
