{block content}
	{snippet content}
		<div class="row-main">
			{embed $templates.'/part/box/header.latte', object: $object, templates: $templates, translator: $translator}
				{block extra}
					{* TODO BE: btns modals *}
					{php $btns = [
						(object) array('link' => '#', 'lang' => 'btn_service_drone', 'naja' => true),
						(object) array('link' => '#', 'lang' => 'btn_have_dji_care', 'naja' => true, 'class' => 'btn--bd')
					]}
					{include $templates.'/part/box/top.latte', btns: $btns, items: $object->cf->header_usp??->items ?? []}
				{/block}
			{/embed}

			{include $templates.'/part/box/service-steps.latte'}
			{include $templates.'/part/box/prices.latte'}

			{control customContentRenderer}

			{php $personCtaCf = $object->cf->person_cta ?? false}
			{embed $templates.'/part/box/person-cta.latte', person: isset($personCtaCf->person) ? $personCtaCf->person->getEntitY() ?? false : false, content: $personCtaCf->content ?? false}
				{block content}
					<p class="tw-flex tw-justify-center tw-gap-[0.8rem_1.2rem] tw-flex-wrap">
						{* TODO BE: modal form *}
						<a href="#" class="btn btn--secondary" data-naja-history="off" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content">
							<span class="btn__text">
								<span class="btn__inner">
									{_"btn_start_picking"}
								</span>
							</span>
						</a>
						<a href="#" class="btn btn--bd" data-naja-history="off" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content">
							<span class="btn__text">
								<span class="btn__inner">
									{_"btn_have_dji_care"}
								</span>
							</span>
						</a>
						<a href="#" class="btn btn--bd" data-naja-history="off" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content">
							<span class="btn__text">
								<span class="btn__inner tw-text-[1.3rem]">
									{_"btn_technician_consultation"|noescape}
								</span>
							</span>
						</a>
					</p>
				{/block}
			{/embed}

			{include $templates.'/part/crossroad/articles-carousel.latte', gradient: true, title: $translator->translate('title_articles_recommended')}
		</div>
	{/snippet}
{/block}
