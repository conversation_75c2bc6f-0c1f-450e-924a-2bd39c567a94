{block content}
	{snippet content}
		<div class="row-main">
			<div class="u-maw-11-12 u-mx-auto u-mb-md u-mb-2xl@md">
				{php $formSuccess = false}

				{if $formSuccess}
					{include $templates.'/part/box/header.latte', class: 'tw-mb-[1.6rem]', name: $translator->translate('title_paid_courses_sent'), annotation: $translator->translate('annot_paid_courses_sent')}

					<div class="grid">
						<div class="grid__cell size--8-12@lg">
							<ol class="grid [counter-reset:step] grid--x-sm grid--y-xs">
								<li class="grid__cell size--6-12@xl [counter-increment:step]">
									<div class="tw-rounded-md md:tw-rounded-xl tw-border-solid tw-border-[0.1rem] tw-border-tile-light tw-p-[2rem] md:tw-p-[5.2rem_6rem] u-mb-last-0 tw-h-full before:tw-content-[counter(step)] before:tw-font-bold before:tw-text-primary before:tw-w-[5.4rem] before:tw-aspect-square before:tw-rounded-full before:tw-bg-primary-150 before:tw-flex before:tw-items-center before:tw-justify-center before:tw-text-[2.2rem] before:tw-mb-[2.4rem] before:tw-font-secondary">
										<h2 class="h3">
											{_"paid_courses_step1_title"}
										</h2>
										<p>
											{_"paid_courses_step1_annot"}
										</p>
										<p>
											<a href="{_'paid_courses_step1_link'}" class="btn btn--lg">
												<span class="btn__text">
													{_"btn_up_login"}
												</span>
											</a>
										</p>
									</div>
								</li>
								<li class="grid__cell size--6-12@xl [counter-increment:step]">
									<div class="tw-rounded-md md:tw-rounded-xl tw-border-solid tw-border-[0.1rem] tw-border-tile-light tw-p-[2rem] md:tw-p-[5.2rem_6rem] u-mb-last-0 tw-h-full before:tw-content-[counter(step)] before:tw-font-bold before:tw-text-primary before:tw-w-[5.4rem] before:tw-aspect-square before:tw-rounded-full before:tw-bg-primary-150 before:tw-flex before:tw-items-center before:tw-justify-center before:tw-text-[2.2rem] before:tw-mb-[2.4rem] before:tw-font-secondary">
										<h2 class="h3">
											{_"paid_courses_step2_title"}
										</h2>
										<p>
											{_"paid_courses_step2_annot"}
										</p>
										<p n:if="isset($pages->paidCoursesQuestionare)">
											<a href="{plink $pages->paidCoursesQuestionare}" class="btn btn--lg">
												<span class="btn__text">
													{_"paid_courses_step2_btn_text"}
												</span>
											</a>
										</p>
									</div>
								</li>
							</ol>
						</div>
						<div class="grid__cell size--4-12@lg">
							{include $templates.'/part/box/help.latte', class: false}
						</div>
					</div>
				{else}
					{include $templates.'/part/box/header.latte', class: 'tw-mb-[1.6rem]', name: 'Pilot dronu - rekvalifikační kurz'}

					<div class="grid">
						<div class="grid__cell size--8-12@lg">
							{* Vybraný kurz *}
							<div class="tw-bg-bg tw-rounded-md md:tw-rounded-lg md:tw-rounded-bl-xl md:tw-rounded-br-xl tw-mb-[1.2rem] md:tw-mb-[2.4rem]">
								<p class="tw-font-bold tw-text-[1.3rem] tw-mb-0 tw-p-[0.8rem_1.6rem]">Vybraný kurz:</p>
								<div class="tw-rounded-md md:tw-rounded-xl tw-bg-white tw-border-solid tw-border-[0.1rem] tw-border-tile-light tw-p-[1.2rem] md:tw-p-[2rem_2.4rem_2rem_2rem]">
									<p class="tw-mb-0 tw-grid tw-gap-[0_2.4rem] tw-grid-cols-[7.2rem_1fr] md:tw-grid-cols-[12.8rem_1fr_auto] tw-items-center">
										<a href="#" class="tw-relative tw-rounded-lg tw-overflow-hidden">
											<img class="img img--4-3" src="/static/img/illust/noimg.svg" alt="" fetchpriority="high">
											{* {include $templates.'/part/core/type.latte', class: 'flag--type flag--sm tw-min-h-[2,2rem] tw-text-[1.1rem]'} *}
											<span class="flag flag--sm flag--type tw-min-h-[2.2rem] tw-text-[1.1rem]">Online</span>
										</a>
										<span>
											<a href="#" class="tw-font-bold">Název kurzu může být klidně na víc řádků</a>
											<span class="tw-text-help tw-block tw-text-[1.4rem]">{_"course_date2"}: pátek 11. 4. 2025, Praha <a href="#" class="tw-text-help">{_"btn_change_date"}</a></span>
											<b class="tw-text-[1.4rem] tw-text-red tw-block">
												{php $freeSpots = 2} {* TODO BE *}
												{capture $pluralLang}{$freeSpots|plural: "last_free_spot_1", "last_free_spot_2", "last_free_spot_3" }{/capture}
												{$freeSpots} {_$pluralLang->__toString()}
											</b>
											<span class="flag flag--red">{_"last_minute_discount"}</span>
										</span>
										<b class="tw-text-right max-md:tw-col-span-2">10 790 Kč</b>
									</p>
								</div>
							</div>

							<form action="?" class="tw-rounded-md md:tw-rounded-xl tw-border-solid tw-border-[0.1rem] tw-border-tile-light tw-p-[2rem] md:tw-p-[5.2rem]">
								<div class="u-maw-4-12 u-mx-auto u-mb-last-0">
									<h2 class="h3 tw-mb-[2rem]">{_"title_paid_courses_form"}</h2>
									{include $templates.'/part/box/order-login.latte'}
									<p>
										TODO: form fieds
									</p>
									<p class="tw-text-center tw-mb-[1.6rem]">
										<button type="submit" class="btn btn--xl btn--secondary">
											<span class="btn__text">
												<span class="btn__inner">
													{_"btn_continue"}
													{('arrow-right-thin')|icon, 'btn__icon'}
												</span>
											</span>
										</button>
									</p>
									<p class="tw-mb-0 tw-text-[1.4rem] tw-text-center">{_"agree_paid_courses_form"}</p>
								</div>
							</form>
						</div>
						<div class="grid__cell size--4-12@lg">
							{include $templates.'/part/box/help.latte', class: false}
						</div>
					</div>
				{/if}
			</div>
		</div>
	{/snippet}
{/block}
