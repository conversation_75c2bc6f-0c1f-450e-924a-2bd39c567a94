{block content}
	{snippet content}
		<div class="row-main">
			{php $letter = 'A'}
			{include $templates.'/part/box/header.latte', class: 'tw-mb-[1.6rem]', name: $seoLink??->name ?? ($object->nameHeading ? $object->nameHeading : $object->name) . ': „'.$letter.'“'}
			{include $templates.'/part/crossroad/alphabet.latte', class: 'tw-mb-[2.8rem] md:tw-mb-[4.8rem]'} {* TODO BE *}

			<div class="u-mb-md u-mb-xl@md">
				<div class="grid grid--x-lg@xxl">
					<div class="grid__cell lg:tw-flex-1">
						<p class="h2 tw-mb-[2.8rem] md:tw-mb-[4.8rem]">{$letter}</p>

						<div n:for="$i = 0; $i < 5; $i++" class="tw-mb-[2.8rem] md:tw-mb-[4.8rem]">
							<p class="tw-font-bold tw-mb-0">AFIX</p>
							<div class="u-mb-last-0">
								<p>
									Zkratka AFIS označuje letištní letovou informační službu (Aerodrome Flight Information Service). Jedná se o důležitou službu v oblasti leteckého provozu, která poskytuje informace pilotům týkající se provozu na letišti a v jeho okolí. Služba AFIS, nebo také zóna AFIS je obvykle využívána na menších letištích, které nejsou vybaveny věží s řízením letového provozu (ATC). I tak ale poskytuje podporu pro bezpečný a efektivní provoz letadel. <a href="#">Zjistit více o AFIS</a>
								</p>
							</div>
						</div>

						<hr class="tw-mb-[2.8rem] md:tw-mb-[4.8rem]">

						<p class="tw-flex tw-flex-wrap tw-items-center tw-justify-between tw-gap-[1.2rem]">
							<a href="#" class="btn btn--gray">
								<span class="btn__text">
									<span class="btn__inner">
										{('angle-left')|icon, 'btn__icon'}
										B
									</span>
								</span>
							</a>
							<a class="max-sm:tw-order-1 max-sm:tw-w-full tw-text-center" n:if="isset($pages->faq)" href="{plink $pages->faq}">{_"btn_back_to_faq"}</a>
							<a href="#" class="btn btn--gray">
								<span class="btn__text">
									<span class="btn__inner">
										D
										{('angle-right')|icon, 'btn__icon'}
									</span>
								</span>
							</a>
						</p>
					</div>
					<div class="grid__cell lg:tw-w-[37rem]">
						{include $templates.'/part/box/help.latte', class: 'b-help--big'}
					</div>
				</div>
			</div>
		</div>
	{/snippet}
{/block}
