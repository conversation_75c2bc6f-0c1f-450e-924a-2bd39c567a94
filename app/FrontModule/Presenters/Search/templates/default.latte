{default $query = ""}
{block content}
	<div class="row-main">
		{php $hasResults = false}
		{dump $hasResults}
		<div n:tag-if="!$hasResults" class="u-maw-8-12 u-mx-auto">
			{include $templates.'/part/box/header.latte', class: 'b-header--simple', name: isset($search) ? $object->name . ' „' . $search . "“" : $object->name, annotation: isset($search) ? ($object->annotation|replace('%term', $search)) : $object->annotation}

			<div class="b-search">
				{if $hasResults}
					<div class="tabs tabs--lg u-mb-sm u-mb-xl@md" data-controller="tabs">
						<div class="tabs__menu">
							{foreach $fulltextResults as $fulltextResult}
								{varType App\Model\FulltextSearch\Result $fulltextResult}
								{php $isZone = $iterator->counter == 2} {* TODO odlišení tabu dronzóny *}
								<a href="#{strtolower($fulltextResult->name)}" n:class="tabs__link, $isZone ? tabs__link--zone, $iterator->isFirst() ? is-selected" data-tabs-target="link" data-action="tabs#changeTab">
									<img n:if="$isZone" src="#" alt="todo" loading="lazy" width="87" height="17">
									{_"search_category_" . strtolower($fulltextResult->name)}
									<span class="tabs__count">{count($fulltextResult->items)}</span>
								</a>
							{/foreach}
						</div>
						{foreach $fulltextResults as $fulltextResult}
							{varType App\Model\FulltextSearch\Result $fulltextResult}
							<div id="{strtolower($fulltextResult->name)}" n:class="tabs__content, $iterator->isFirst() ? is-active" data-tabs-target="content">
								{switch strtolower($fulltextResult->name)}
									{case 'writerlocalization'} {* == products *}
										<div class="u-pt-md">
											TODO: filtrace, bannrey ve výpisu, předání třídy c-products--search do výpisu
											{snippet products}
												{control catalogProducts}
											{/snippet}
										</div>
									{case 'publisherlocalization'}
										<div class="u-pt-sm">
											<p class="b-search__btns u-mb-xs u-mb-md@md">
												<a href="#" class="btn btn--bd"><span class="btn__text">Vše</span></a>
												<a href="#" class="btn btn--gray"><span class="btn__text">Recenze</span></a>
												<a href="#" class="btn btn--gray"><span class="btn__text">Návody</span></a>
											</p>
											TODO výpis + paging
										</div>
									{case 'taglocalization'} {* == other *}
										<div class="u-pt-md">
											<div class="grid grid--x-xs grid--y-xs grid--x-sm@md grid--y-sm@md">
												{for $i = 0; $i < 10; $i++}
													<div class="grid__cell size--6-12@sm size--4-12@lg size--3-12@xl">
														{include $templates.'/part/box/other.latte'}
													</div>
												{/for}
											</div>
											<p class="u-mb-0">
												TODO: paging
											</p>
										</div>
								{/switch}
							</div>
						{/foreach}
					</div>
				{else}
					{include $templates.'/part/box/tips.latte', class: 'u-maw-6-12 u-mx-auto', cf: $object->cf->emptyResultsTips ?? false}
				{/if}
			</div>
		</div>
	</div>

{/block}

