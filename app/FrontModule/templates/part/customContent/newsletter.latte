{default $title = $customContentItem->title ?? false}
{default $annot = $customContentItem->annot ?? false}

{* TODO control pro CC newsletter *}
{default $class = 'tw-mb-[3.2rem] md:tw-mb-[5.6rem]'}
<form action="?" n:class="$class, 'tw-flex max-lg:tw-flex-col max-lg:tw-text-center tw-items-center max-lg:tw-gap-[1.2rem] tw-bg-blue tw-text-white tw-rounded-[1.2rem] md:tw-rounded-[2.4rem] tw-p-[3.2rem] md:tw-p-[4.8rem] lg:tw-p-[4.8rem_4.8rem_1.6rem]'">
	<div class="tw-flex-[1_1_50%] tw-max-w-[34.9rem]">
		<p class="tw-font-tertiary tw-mb-[0.4rem] tw-text-[4.3rem] tw-leading-[0.85]">
			{$title}
		</p>
		<p class="tw-mb-0 tw-text-[1.4rem]">
			{$annot}
		</p>
	</div>
	<div>
		TODO: inp + btn
		<p n:if="$pages->personalData ?? false" class="tw-mb-0 tw-text-[1.4rem] [&_a]:tw-transition-opacity [&_a]:tw-no-underline [&_a]:tw-duration-150 [&_a]:tw-text-white [&_a]:tw-opacity-[0.5] hover:[&_a]:tw-opacity-[1] hover:[&_a]:tw-text-white">
			{_"newsletter_agree"|noescape|replace:'%link', $presenter->link($pages->personalData)}
		</p>
	</div>
</form>
