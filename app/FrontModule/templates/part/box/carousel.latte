{default $class = false}
{default $cf = $object->cf->carousel ?? []}

{php $slides = []}
{foreach $cf as $slide}
	{php $imageDesktop = isset($slide->images->desktop) ? $slide->images->desktop->getEntity() ?? false : false}
	{php $imageTablet = isset($slide->images->tablet) ? $slide->images->tablet->getEntity() ?? false : false}
	{php $imageMobile = isset($slide->images->mobile) ? $slide->images->mobile->getEntity() ?? false : false}

	{if $imageDesktop}
		{php $type = $slide->link->toggle}
		{php $page = isset($slide->link->systemHref) && isset($slide->link->systemHref->page) ? $slide->link->systemHref->page->getEntity() ?? false : false}
		{php $hrefName = ($slide->link->systemHref??->hrefName ?? false) ?: ($slide->link->customHref??->hrefName ?? false)}
		{php $href = $slide->link->customHref??->href ?? false}

		{if $page}{capture $linkText}{plink $page}{/capture}{/if}
		{php $link = $page ? $linkText->__toString() : $href}

		{if $type == 'systemHref' && $page}
			{php $text = $hrefName ? $hrefName : $page->nameAnchor}
		{else}
			{php $text = $hrefName}
		{/if}

		{php array_push($slides, (object) array('imageDesktop' => $imageDesktop, 'imageTablet' => $imageTablet, 'imageMobile' => $imageMobile, 'link' => $link, 'text' => $text))}

	{/if}
{/foreach}

<div n:if="count($slides)" n:class="b-carousel, $class, embla" data-controller="carousel">
	<div class="b-carousel__carousel">
		<div class="embla__viewport" data-carousel-target="viewport">
			<div class="b-carousel__grid grid grid--scroll grid--x-0 grid--y-0 embla__container">
				{foreach $slides as $slide}
					{var $viewportId = $presenter->fireEventViewPromotion($text, 'top_' . $iterator->counter0)}
					<div data-gtm-viewport-script-id-value="{$viewportId}" data-controller="gtm-viewport" class="grid__cell">
						<p n:class="b-carousel__img, !$slide->link ? 'img img--2-1', u-mb-0">
							<a n:tag-if="$slide->link" href="{$slide->link}" class="b-carousel__link img img--2-1 bnr-click"  data-list-id="{$viewportId}" data-list-name="{$text}">
								<picture n:if="$imageDesktop">
									<source n:if="$slide->imageDesktop" media="(min-width: 1200px)" srcset="{$slide->imageDesktop->getSize('xl')->src}">
									<source n:if="$slide->imageTablet" media="(min-width: 750px)" srcset="{$slide->imageTablet->getSize('xl')->src}">
									<source media="(min-width: 480px)" srcset="{if $slide->imageMobile}{$slide->imageMobile->getSize('lg')->src}{else}{$slide->imageDesktop->getSize('lg')->src}{/if}">
									<img class="img__media" src="{if $slide->imageMobile}{$slide->imageMobile->getSize('md')->src}{else}{$slide->imageDesktop->getSize('md')->src}{/if}" alt="{$slide->imageDesktop->getAlt($mutation)}"{if $iterator->first} fetchpriority="high"{/if}>
								</picture>
							</a>
						</p>
					</div>
				{/foreach}
			</div>
			<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="carousel#prev" data-carousel-target="prevButton">
				{('arrow-left')|icon}
				<span class="u-vhide">{_btn_prev}</span>
			</button>
			<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="carousel#next" data-carousel-target="nextButton">
				{('arrow-right')|icon}
				<span class="u-vhide">{_btn_next}</span>
			</button>
		</div>

		<div class="embla__dots is-disabled" data-carousel-target="dots"></div>
		<script type="text/template" class="embla-dot-template">
			<button class="embla__dot" type="button"><span class="embla__dot-inner">%i</span></button>
		</script>
	</div>
	<ul class="b-carousel__list">
		{foreach $slides as $slide}
			<li n:class="b-carousel__item, $iterator->first ? is-active" data-carousel-target="navItem">
				<button type="button" class="b-carousel__link as-link" data-action="carousel#handleBtnClick">
					<span>
						{$slide->text}
					</span>
				</button>
			</li>
		{/foreach}
		<li n:if="isset($pages->discount)" class="b-carousel__item">
			<a href="{plink $pages->discount}" class="b-carousel__link b-carousel__link--more as-link">
				<span>
					{_"carousel_show_all_discounts"}
				</span>
			</a>
		</li>
	</ul>
</div>
