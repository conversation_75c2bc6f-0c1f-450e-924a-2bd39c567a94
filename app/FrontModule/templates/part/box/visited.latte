{default $class = false}
{default $products = []}

<div n:snippet="productList" n:if="count($products)" n:class="c-visited, $class"
	data-controller="gtm-viewport"
	data-gtm-viewport-script-id-value="{$control->getName()}"
	data-gtm-viewport-item-class-value="b-lastVisited-product"
	data-gtm-viewport-item-event-value="{$gtmItemEvent}"
>
	<h2 class="h3 tw-mb-[1.2rem]">{_"product_list_last_visited"}</h2>

	<div class="c-visited__carousel embla tw-overflow-visible" data-controller="embla">
		<div class="embla__viewport" data-embla-target="viewport">
			<ul class="c-visited__grid grid grid--scroll grid--x-0 grid--y-0 embla__container">
				<li n:foreach="$products as $product" class="grid__cell b-lastVisited-product tw-w-[23rem]" data-gtm-event="view_item_list" data-gtm-item="{$productsGtmItem[$product->id]}">
					{varType App\Model\Orm\Product\Product $product}
					{var $priceVat = $product->priceVat($mutation, $priceLevel, $state)}
					{var $priceInfo = $product->getPriceInfo($mutation, $priceLevel, $state)}
					{var $oldPriceVat = $priceInfo->getOldPrice()}

					{cache cacheKey('lastVisitedItem', $product), expire: $product->getTemplateCacheExpire(), tags: $product->getTemplateCacheTags()}
						<p class="tw-flex tw-flex-col tw-items-center tw-p-[2.4rem_2rem] tw-text-center tw-font-bold tw-text-[1.3rem] md:tw-text-[1.5rem] u-mb-0 link-mask tw-h-full {if !$iterator->isLast()}after:tw-content-[''] after:tw-absolute after:tw-bg-bg after:tw-top-[3.2rem] after:tw-bottom-[3.2rem] after:tw-right-0 after:tw-w-[0.1rem]{/if}">

							{if $product->firstImage}
								{php $img = $product->firstImage->getSize('sm')}
								<img class="img img--4-3 tw-w-[15.2rem] tw-mx-auto tw-mb-[2rem] tw-rounded-md" src="{$img->src}" alt="{$product->nameAnchor}" loading="lazy">
							{else}
								<img class="img img--4-3 tw-w-[15.2rem] tw-mx-auto tw-mb-[2rem] tw-rounded-md" src="/static/img/illust/noimg.svg" alt="{$product->nameAnchor}" loading="lazy">
							{/if}

							<img n:if="$oldPriceVat !== null" class="tw-absolute tw-top-[2rem] tw-right-[2rem]" src="/static/img/illust/discount.svg" alt="" loading="lazy">

							<a href="{plink $product}" class="link-mask__link tw-no-underline tw-mb-[0.8rem]" data-id="{$product->id}" data-list-id="last_visited" data-list-name="{_'product_list_last_visited'}">
								{$product->nameAnchor}
							</a>
							<s class="tw-text-[1.1rem] tw-mt-auto u-c-help u-d-b" n:if="$oldPriceVat !== null">{$oldPriceVat|money}</s>
							<b n:class="!($oldPriceVat !== null) ? tw-mt-auto">{$priceVat|money}</b>
						</p>
					{/cache}
				</li>
			</ul>
		</div>
		<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
			{('arrow-left')|icon}
			<span class="u-vhide">{_btn_prev}</span>
		</button>
		<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
			{('arrow-right')|icon}
			<span class="u-vhide">{_btn_next}</span>
		</button>
	</div>
</div>
