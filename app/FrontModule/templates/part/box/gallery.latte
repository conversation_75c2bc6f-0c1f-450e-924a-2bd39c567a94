{default $class = 'tw-mb-[3.2rem] md:tw-mb-[5.6rem]'}
{default $images = []}
{default $type = 'carousel'}

{php $isCarousel = $type == 'carousel'}

{if $isCarousel}
	<p n:class="$class, 'embla tw-overflow-visible'" data-controller="embla">
		<span data-embla-target="viewport">
			<span class="grid embla__container">
				{foreach $images as $image}
					<a href="{$image->getSize('max')->src}" data-modal='{"gallery": "gallery"}' class="grid__cell">
						<img class="img img--3-2 tw-rounded-[0.6rem] md:tw-rounded-[1.2rem]" srcset="
							{$image->getSize('md-3-2')->src} 560w,
							{$image->getSize('lg-3-2')->src} 750w,
							{$image->getSize('xl-3-2')->src} 1400w,
							{$image->getSize('2xl-3-2')->src} 1920"
						sizes="(max-width: 1000px) 100vw,
								888px"
						src="{$image->getSize('2xl-3-2')->src}"
						alt="{$image->getAlt($mutation)}" loading="lazy">
					</a>
				{/foreach}
			</span>
		</span>
	</p>
{else}
	<p n:class="$class, 'tw-flex tw-flex-wrap tw-gap-[var(--grid-gutter)]'">
		{foreach $images as $image}
			<a href="{$image->getSize('max')->src}" data-modal='{"gallery": "gallery"}'>
				{* todo width & height *}
				<img class="tw-rounded-[0.6rem] tw-h-[11rem] md:tw-h-[15.3rem]" src="{$image->getSize('md')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
			</a>
		{/foreach}
	</p>
{/if}

