{default $class = 'u-mb-sm u-mb-xl@md'}
{default $cf = $object->cf->service_steps ?? false}

{if $cf}
	{embed $templates.'/part/box/steps.latte', class: 'u-mb-sm u-mb-xl@md', items: $cf->steps ?? [], title: $cf->title ?? false, bd: true, object: $object, templates: $templates}
		{block rightExtra}
			<p class="tw-mb-0">
				{* TODO BE: form modal *}
				<a href="#" class="btn btn--secondary btn--lg" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content">
					<span class="btn__text">
						{_"btn_service_drone"}
					</span>
				</a>
			</p>
		{/block}
		{block bottomExtra}
			{php $boxes = $cf->boxes ?? []}
			<div n:if="count($boxes)" class="grid grid--x-xs grid--y-xs grid--x-sm@md grid--y-sm@md">
				<div n:foreach="$boxes as $box" class="grid__cell size--6-12@lg">
					<div class="tw-bg-bg tw-rounded-md md:tw-rounded-xl tw-p-[2rem_2.4rem_2.4rem] md:tw-p-[3.2rem_3.2rem_3.2rem_4.4rem] tw-h-full">
						<div class="tw-flex tw-mb-[0.8rem] md:tw-mb-[1.2rem]">
							<div class="tw-flex-1 u-mb-last-0">
								<h3 n:if="$box->title ?? false" class="h4 tw-mb-[0.4rem]">{$box->title}</h3>
								<p n:if="$box->annot ?? false" class="tw-text-[1.4rem]">
									{$box->annot|texy|noescape}
								</p>
							</div>
							{php $img = isset($box->image) ? $box->image->getEntity() ?? false : false}
							<img n:if="$img" class="tw-flex-[0_0_auto] tw-w-[10rem] md:tw-w-[13rem] img img--4-3" src="{$img->getSize('sm')->src}" alt="" loading="lazy" width="130" height="98">
						</div>
						{php $btn = $box->btn ?? false}
						{php $type = $btn??->type ?? false}
						{php $custom = $btn->custom ?? false}
						<p n:if="$type" class="tw-mb-0">
							{if $type == 'login'}
								<a href="{plink $pages->userLogin}" class="btn btn--bd btn--gray btn--sm" data-modal='{"medium": "fetch", "modalClass": "b-modal--xs"}' data-snippetid="snippet--userFormArea">
									<span class="btn__text">
										{_"btn_login"}
									</span>
								</a>
							{elseif $type == 'custom' && $custom}
								{php $toggle = $custom->toggle}
								{php $page = isset($custom->systemHref) && isset($custom->systemHref->page) ? $custom->systemHref->page->getEntity() ?? false : false}
								{php $href = $custom->customHref??->href ?? false}
								{php $hrefNameSystem = $custom->systemHref??->hrefName ?? false}
								{php $hrefNameCustom = $custom->customHref??->hrefName ?? false}

								{if $toggle == 'systemHref' && $page}
									<a href="{plink $page}" n:ifcontent class="btn btn--bd btn--gray btn--sm">
										<span class="btn__text">
											{if $hrefNameSystem}
												{$hrefNameSystem}
											{else}
												{$page->nameAnchor}
											{/if}
										</span>
									</a>
								{elseif $toggle == 'customHref' && $href && $hrefNameCustom}
									<a href="{$href}" class="btn btn--bd btn--gray btn--sm" target="_blank" rel="noopener noreferrer">
										<span class="btn__text">
											{$hrefNameCustom}
										</span>
									</a>
								{/if}
							{/if}
						</p>
					</div>
				</div>
			</div>
		{/block}
	{/embed}
{/if}
