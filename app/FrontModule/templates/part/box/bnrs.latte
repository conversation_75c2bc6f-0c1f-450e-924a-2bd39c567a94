{default $class = 'tw-mb-[3.2rem] md:tw-mb-[5.6rem]'}
{default $items = []}

{* css grid has:nth-child *}

{if count($items)}
	{if count($items) == 1}
		{php $class .= ' '}
	{elseif count($items) == 2}
		{php $class .= ' tw-grid md:tw-grid-cols-2 tw-gap-[var(--grid-gutter)]'}
	{else}
		{php $class .= ' tw-grid md:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-[var(--grid-gutter)]'}
	{/if}

	<div n:class="$class">
		{foreach $items as $item}
			{php $color = $item->color ?? 'blue'}

			<p class="tw-mb-0 tw-flex-1">
				{embed $templates.'/part/core/linkChoose.latte', class: 'tw-flex tw-items-center tw-p-[2.4rem] tw-rounded-[0.6rem] md:tw-rounded-[1.2rem] tw-border-[0.1rem] tw-border-solid tw-border-bd tw-no-underline tw-transition-[border-color] tw-duration-150 hover:tw-text-text hover:tw-border-text', linkChoose: $item->link ?? false, customContent: true, presenter: $presenter}
					{block content}
						<span class="tw-flex-1">
							<span class="h3 tw-block tw-mb-0">
								{$item->title ?? false}
							</span>
							<span class="tw-font-secondary tw-font-medium tw-text-[1.4rem]">
								{$item->desc ?? false}
							</span>
						</span>

						{php $color = $item->color ?? 'blue'}
						{switch $color}
							{case 'blue'}
								{php $iconClass = 'tw-bg-blue'}
							{case 'green'}
								{php $iconClass = 'tw-bg-green'}
							{case 'brown'}
								{php $iconClass = 'tw-bg-brown'}
						{/switch}
						{php $icon = isset($item->icon) ? $item->icon->getEntity() ?? false : false}
						<span n:if="$icon" n:class="$iconClass, 'tw-rounded-[0.6rem] tw-size-[4.8rem] tw-flex tw-items-center tw-justify-center tw-text-white'">
							<img src="{$icon->getSize('xs')->src}" alt="{$icon->getAlt($mutation)}" class="tw-size-[2.4rem] tw-filter tw-invert tw-brightness-0" width="24" height="24">
						</span>
					{/block}
				{/embed}
			</p>
		{/foreach}
	</div>
{/if}
