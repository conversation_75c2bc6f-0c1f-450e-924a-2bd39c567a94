{default $class = false}
{default $discount = false}
{default $lazyLoading = true}

<div n:if="$discount" n:class="b-discount, $class, link-mask, box">
	{php $image = isset($discount->cf->gallery) && isset($discount->cf->gallery->mainImage) ? $discount->cf->gallery->mainImage->getEntity() ?? false : false}
	<p class="b-discount__img img img--2-1">
		{if $image}
			<img srcset="
					{$image->getSize('sm')->src} 320w,
					{$image->getSize('md')->src} 560w,
					{$image->getSize('lg')->src} 750w,
					{$image->getSize('xl')->src} 1400w"
				sizes="(max-width: 30rem) 100vw,
						(max-width: 91.25rem) 50vw,
						68rem"
				src="{$image->getSize('xl')->src}"
				alt="{$discount->name}"{if $lazyLoading} loading="lazy"{else} fetchpriority="high"{/if}>
		{else}
			<img src="/static/img/illust/noimg.svg" alt=""{if $lazyLoading} loading="lazy"{else} fetchpriority="high"{/if}>
		{/if}
	</p>
	<div class="b-discount__main">
		<h2 class="b-discount__title h3">
			{$discount->name}
		</h2>
		<p n:if="$discount->description ?? false" class="b-discount__annot">
			{$discount->description}
		</p>
		<p class="b-discount__bottom">
			<a href="{plink $discount}" class="btn btn--sm btn--secondary link-mask__link">
				<span class="btn__text">
					{_"btn_show_discount"}
				</span>
			</a>
			<span class="b-discount__countdown">
				{capture $month}{$discount->publicTo|date:'F'}{/capture}
				{_"discount_countdown"} <b>{$discount->publicTo|date:'j.'} {_$month->__toString()} {$discount->publicTo|date:'Y'}</b>
			</span>
		</p>
	</div>
</div>
