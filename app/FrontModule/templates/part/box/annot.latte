{default $class = 'u-maw-8-12 tw-mx-auto tw-mb-[3.2rem] md:tw-mb-[5.6rem]'}
{default $name = $seoLink??->name ?? (($object->nameHeading ?? '') !== '' ? $object->nameHeading : $object->name)}
{default $annotation = $seoLink??->description ?? $object??->annotation ?? null}
{default $img = isset($object->cf->mainImage->image) ? $object->cf->mainImage->image->getEntity() ?? false : false}

<header n:ifcontent n:class="$class, 'u-mb-last-0'">
	<div class="u-mb-last-0 u-maw-6-12 tw-mx-auto tw-mb-[3.2rem]">
		{control breadcrumb, [class: 'tw-mb-[1.6rem]']}
		<h1 n:if="$name" class="tw-mt-0 tw-mb-[1.6rem]">
			{control seoTools, $name} {* SeoTools/catalog.latte / SeoTools/common.latte *}
		</h1>
	</div>
	<figure n:if="$img" class="tw-mb-[3.2rem] md:tw-mb-[5.6rem]">
		<img class="img img--2-1 tw-rounded-[0.6rem] md:tw-rounded-[1.2rem]" srcset="
			{$img->getSize('md-2-1')->src} 560w,
			{$img->getSize('lg-2-1')->src} 750w,
			{$img->getSize('xl-2-1')->src} 1400w,
			{$img->getSize('2xl-2-1')->src} 1920"
		sizes="(max-width: 1220px) 100vw,
				680px"
		src="{$img->getSize('2xl-2-1')->src}"
		alt="{$img->getAlt($mutation)}" fetchpriority="high">
	</figure>
	<div n:ifcontent class="h2 tw-mt-0 tw-mb-0 u-mb-last-0 u-maw-6-12 tw-mx-auto">
		{$annotation|tables|lazyLoading|obfuscateEmailAddresses|noescape}
	</div>
</header>

{* <p n:if="$date ?? false" class="b-annot__date">
	{$object->publicFrom|date:"j. n. Y"}
</p> *}

{* todo tags, author, actualized, reading time *}
