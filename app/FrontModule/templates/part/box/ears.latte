{default $class = 'tw-mb-[3.2rem] md:tw-mb-[5.6rem]'}
{default $text = false}
{default $bg = 'light-blue'}

{php $secondaryColor = false}
{switch $bg}
	{case 'blue'}
		{php $class .= ' tw-bg-blue tw-text-white'}
	{case 'light-blue'}
		{php $class .= ' tw-bg-blue-10'}
		{php $secondaryColor = 'tw-text-medior-blue'}
{/switch}

<div n:class="$class, 'tw-relative tw-p-[3.2rem] md:tw-p-[6.4rem_5.6rem] lg:tw-p-[6.4rem_26rem_6.4rem_5.6rem] tw-rounded-[0.6rem] md:tw-rounded-[1.2rem] tw-max-w-full lg:tw-aspect-[888/475]'">
	<p n:class="'tw-font-tertiary tw-mb-[2.8rem] tw-leading-[1] tw-text-[4.3rem] md:tw-text-[8.5rem]', $secondaryColor">
		{_"ears_title"}
	</p>
	<p class="tw-mb-0 tw-font-secondary tw-font-medium tw-text-[1.8rem] md:tw-text-[2.6rem] tw-leading-[1.5]">
		{$text}
	</p>
	<img class="tw-absolute tw-right-0 tw-bottom-0 tw-w-[27.7%] max-lg:tw-hidden" src="/static/img/illust/ears.webp" alt="" loading="lazy" width="246" height="386">
</div>
