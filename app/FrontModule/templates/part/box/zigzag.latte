{default $class = 'tw-mb-[3.2rem] md:tw-mb-[5.6rem]'}
{default $items = []}

<div n:if="count($items)" n:class="$class">
	<div n:foreach="$items as $item" class="tw-mb-[3.2rem] md:tw-mb-[5.6rem]">
		<div class="tw-grid md:tw-grid-cols-2 tw-gap-[3.2rem] md:tw-gap-[5.6rem]">
			<div>
				{php $img = isset($item->image) ? $item->image->getEntity() ?? false : false}
				<p n:if="$img" class="tw-mb-0">
					{* TODO width, height *}
					<img class="tw-rounded-[0.6rem] md:tw-rounded-[1.2rem]" src="{$img->getSize('lg')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
				</p>
			</div>
			<div n:class="$iterator->isEven() ? 'md:tw-order-[-1]'">
				{include $templates.'/part/box/content.latte', class: false, content: $item->content ?? false}
			</div>
		</div>
	</div>
</div>
