{default $class = false}
{php $product = isset($item->link) ? $item->link->getEntity() ?? false : false}

<div n:if="$product" n:class="b-product-condensed, $class, link-mask">
	{php $productLocalization = $product->getLocalization($mutation)}
	<p class="b-product-condensed__img">
		{if $product->firstImage}
			<img class="img img--contain img--4-3" src="{$product->firstImage->getSize('sm')->src}" alt="" loading="lazy" width="126" height="95">
		{else}
			<img class="img img--contain img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy" width="126" height="95">
		{/if}
	</p>
	<p class="b-product-condensed__content">
		<span n:if="$item->flag->text ?? false" n:class="b-product-condensed__flag, flag, ($item->flag->type ?? false) ? 'flag--' . $item->flag->type">
			{$item->flag->text}
		</span>
		<span class="b-product-condensed__title">
			<a href="{plink $product}" class="b-product-condensed__link link-mask__link">
				{$productLocalization->nameAnchor}
			</a>
			{if $item->tooltip ?? false}
				{embed $templates.'/part/core/tooltip.latte', class: 'b-product-condensed__tooltip tooltip--gray-light link-mask__unmask', btnClass=>'as-link', placement: 'right'}
					{block btn}{('info')|icon}{/block}
					{block content}
						{$item->tooltip|texy|noescape}
						<a href="{plink $product}" class="u-pt-sm btn btn--bd">
							<span class="btn__text">
								{_"btn_find_more"}
							</span>
						</a>
					{/block}
				{/embed}
			{/if}
		</span>
	</p>
</div>
