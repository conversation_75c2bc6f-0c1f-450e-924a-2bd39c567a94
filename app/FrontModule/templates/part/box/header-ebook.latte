{default $class = false}
{default $cf = $object->cf->header_ebook ?? false}

<header n:class="b-header-ebook, $class, u-maw-8-12, u-mx-auto">
	{snippetArea breadcrumbArea}
		{control breadcrumb, [class: 'u-pt-sm u-pt-md@md']}
	{/snippetArea}
	<div class="b-header-ebook__holder">
		<div class="grid grid--middle grid--y-xs">
			<div class="grid__cell size--6-12@lg u-mb-last-0">
				<h1 class="b-header-ebook__title">
					{$object->nameHeading ? $object->nameHeading : $object->name}
				</h1>
				<p n:if="$object->annotation ?? false">
					{$object->annotation|texy|noescape}
				</p>
			</div>
			<div class="grid__cell size--6-12@lg">
				{php $img = isset($cf->image) ? $cf->image->getEntity() ?? false : false}
				<p class="u-mb-0">
					{* <img class="img img--4-3 img--contain" src="{$img->getSize('lg')->src}" alt="{$img->getAlt($mutation)}" fetchpriority="high"> *}
					<img class="b-header-ebook__img img img--4-3 img--contain" srcset="
						{$img->getSize('sm')->src} 320w,
						{$img->getSize('md')->src} 560w,
						{$img->getSize('lg')->src} 750w,
						{$img->getSize('xl')->src} 1400w"
					sizes="(max-width: 636px) 100vw,
							(max-width: 1000px) 490px,
							(max-width: 12000px) 50vw,
							490px"
					src="{$img->getSize('lg')->src}"
					alt="{$img->getAlt($mutation)}" fetchpriority="high">
				</p>
			</div>
		</div>
	</div>
	{* TODO link na stažení *}
	{php $file = isset($cf->file) ? $cf->file->getEntity() ?? false : false}
	{php $btns = [(object) array('link' => $file ? $file->url : '#', 'lang' => 'btn_download_ebook', 'icon' => 'download', 'download' => true)]}

	{include $templates.'/part/box/top.latte', class: 'b-header-ebook__top', btns: $btns, items: $cf->usp ?? []}
</header>
