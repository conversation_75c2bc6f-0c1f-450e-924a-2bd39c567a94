{default $class = false}
{default $title = false}
{default $items = []}
{default $modalGallery = 'showcase'}

<div n:class="$class" data-controller="show-more">
	<h2 n:if="$title" class="md:tw-text-center tw-mb-[1.2rem] md:tw-mb-[2.4rem]">{$title}</h2>
	<div class="tw-grid md:tw-grid-cols-12 tw-gap-[0.8rem] md:tw-gap-[2rem]">
		{foreach $items as $item}
			{php $firstItemClass = 'md:tw-col-span-12 lg:tw-col-span-8 lg:tw-row-span-2 tw-aspect-[16/9] lg:tw-aspect-auto'}
			{php $itemClass = 'md:tw-col-span-6 lg:tw-col-span-4 tw-aspect-[16/9]'}
			{php $type = $item->toggle ?? false}

			<div n:ifcontent n:if="$type" n:class="$iterator->isFirst() ? $firstItemClass : $itemClass, $iterator->counter > 3 ? 'u-d-n'" data-show-more-target="hidden">
				{if $type == 'article'} {* TOOD *}
					{* Článek *}
					{php $page = isset($item->article->page) ? $item->article->page->getEntity() ?? false : false}

					{if $page}
						{php $img = isset($page->cf->base->crossroadImage) ? $page->cf->base->crossroadImage->getEntity() : false}
						{php $src = $img ? ($iterator->isFirst() ? $img->getSize('xl')->src : $img->getSize('lg')->src) : false}

						{include #article, class: $iterator->isFirst() ? 'b-article-inside--lg' : false, page: $page, src: $src}
					{/if}
				{elseif $type == 'video'}
					{* Video *}
					{php $poster = isset($item->video->image) ? $item->video->image->getEntity() ?? false : false}
					{php $link = $item->video->link ?? false}

					{include #video, link: $link, poster: $poster, maxSize: $iterator->isFirst() ? 1540 : 750, posterSize: $iterator->isFirst() ? 'xl' : 'lg', modalGallery: $modalGallery}
				{elseif $type == 'photo'}
					{* Obrázek *}
					{php $img = isset($item->photo->image) ? $item->photo->image->getEntity() ?? false : false}
					{php $src = $img->getSize('2xl')->src}
					{php $srcThumb = $iterator->isFirst() ? $img->getSize('xl')->src : $img->getSize('lg')->src}

					{include #image, src: $src, srcThumb: $srcThumb, modalGallery: $modalGallery, alt: $img->getAlt($mutation)}
				{/if}
			</div>
		{/foreach}
	</div>

	<p class="md:tw-text-center tw-mb-0 tw-pt-[1.2rem] md:tw-pt-[2.4rem]" data-show-more-target="showMoreBtn">
		<button type="button" class="btn btn--bd" data-action="show-more#loadMore">
			<span class="btn__text">
				{_"btn_show_more_showcase"}
			</span>
		</button>
	</p>
</div>

{define #video}
	{default $link = false}
	{default $poster = false}
	{default $maxSize = 750}
	{default $posterSize = 'lg'}
	{default $modalGallery = ''}

	<p n:if="$link" class="tw-h-full u-mb-0">
		<a class="tw-h-full" href="{$link}" data-modal='{"gallery": "{$modalGallery}"}'>
			{include $templates.'/part/box/video.latte', class: 'tw-h-full tw-rounded-md md:tw-rounded-xl tw-overflow-hidden', maxSize: $maxSize, posterSize: $posterSize, poster: $poster, link: $link}
		</a>
	</p>
{/define}

{define #article}
	{default $class = false}
	{default $page = false}
	{default $src = false}

	{if $page}
		{include $templates.'/part/box/article-inside.latte', class: $class . ' tw-w-full tw-h-full', page: $page, imgSrc: $src, showTags: false, showAuthor: false, showDays: false}
	{/if}
{/define}

{define #image}
	{default $src = false}
	{default $alt = false}
	{default $imgClass = false}
	{default $srcThumb = false}
	{default $modalGallery = ''}
	<p n:if="$src && $srcThumb" class="tw-h-full u-mb-0 tw-rounded-md md:tw-rounded-xl tw-overflow-hidden">
		<a class="tw-h-full" href="{$src}" data-modal='{"gallery": "{$modalGallery}"}'>
			<img n:class="img, img--16-9, 'hover:tw-scale-[1.05] tw-transition-transform tw-duration-150'" src="{$srcThumb}" alt="{$alt}" loading="lazy">
		</a>
	</p>
{/define}
