{varType App\Model\Orm\Order\Order $order}
{default $class = 'tw-mb-[2.4rem] md:tw-mb-[3.2rem]'}

<div n:if="!$order->isEmpty()" n:class="$class, u-mb-last-0">
{* todo course type *}

	{* Produkty *}
	{if count($order->products)}
		<h2 class="h3 tw-mb-[1.2rem]">{_"title_order_detail_products"}</h2>
		<table class="c-table tw-grid-cols-[9.5rem_1fr_1fr] lg:tw-grid-cols-[10.3rem_1fr_auto_auto_auto_auto] xxl:tw-grid-cols-[15.9rem_1fr_15rem_15rem_15rem_auto] tw-text-[1.3rem] md:tw-text-[1.5rem] tw-mb-[2.4rem] md:tw-mb-[3.2rem]">
			<tbody>
				<tr n:foreach="$order->products as $productItem" class="tw-gap-[0_1.2rem] lg:tw-gap-[2rem] tw-p-[1.2rem_1.6rem_1.6rem] lg:tw-p-[1.2rem_2.8rem_1.2rem_2rem]">
					{varType App\Model\Orm\Order\Product\ProductItem $productItem}
					{var $link = $presenter->link($productItem->variant->product, ['v' => $productItem->variant->id])}

					<td>
						<a href="{$link}">
							{if $productItem->variant && $productItem->variant->firstImage}
								<img class="img img--4-3 tw-rounded-lg" src="{$productItem->variant->firstImage->getSize('sm')->src}" alt="" loading="lazy">
							{else}
								<img class="img img--4-3 tw-rounded-lg" src="/static/img/illust/noimg.svg" alt="" loading="lazy"/>
							{/if}
						</a>
					</td>
					<td class="max-lg:tw-col-start-2 max-lg:tw-col-end-4">
						<a href="{$link}" class="tw-font-bold tw-block">{$productItem->getName()}</a>
						<span n:ifcontent class="tw-text-help md:tw-text-[1.4rem]">
							{* {include $templates.'/part/core/type.latte', product: $productItem->variant->product} *}
							{* {$productItem->getDesc()} *}
						</span>
					</td>
					<td class="max-lg:tw-mt-[0.8rem] max-lg:tw-col-start-1 max-lg:tw-col-end-4 md:tw-text-center max-lg:tw-flex tw-items-center tw-justify-between">
						<span class="tw-text-help md:tw-text-[1.2rem] tw-block md:tw-mb-[0.4rem]">{_"table_amount"}</span>
						{$productItem->amount} {_"stock_piece"}
					</td>
					<td class="max-lg:tw-col-start-1 max-lg:tw-col-end-4 md:tw-text-center max-lg:tw-flex tw-items-center tw-justify-between">
						<span class="tw-text-help md:tw-text-[1.2rem] tw-block md:tw-mb-[0.4rem]">{_"table_price_per_unit"}</span>
						{* {$productItem->unitPrice|money} *} {* TODO BE: formát je price misto money *}
					</td>
					<td class="max-lg:tw-mb-[0.8rem] max-lg:tw-col-start-1 max-lg:tw-col-end-4 md:tw-text-center max-lg:tw-flex tw-items-center tw-justify-between">
						<span class="tw-text-help md:tw-text-[1.2rem] tw-block md:tw-mb-[0.4rem]">{_"table_total_price"}</span>
						<b>{$productItem->totalPrice|money}</b>
					</td>
					<td class="max-md:tw-col-start-1 max-lg:tw-col-end-4 tw-text-right">
						{* TODO BE *}
						<a href="#" class="btn btn--bd btn--sm">
							<span class="btn__text">
								{_"btn_claims"}
							</span>
						</a>
					</td>
				</tr>
			</tbody>
		</table>
	{/if}

	{* Kurzy *}
	{if count($order->classEvents)}
		<h2 class="h3 tw-mb-[1.2rem]">{_"title_order_detail_courses"}</h2>
		<table class="c-table tw-grid-cols-[9.5rem_1fr_1fr] lg:tw-grid-cols-[10.3rem_1fr_auto_auto_auto_auto] xxl:tw-grid-cols-[15.9rem_1fr_15rem_15rem_15rem_auto] tw-text-[1.3rem] md:tw-text-[1.5rem] tw-mb-[2.4rem] md:tw-mb-[3.2rem]">
			<tbody>
				<tr n:foreach="$order->classEvents as $classItem" class="tw-gap-[0_1.2rem] lg:tw-gap-[2rem] tw-p-[1.2rem_1.6rem_1.6rem] lg:tw-p-[1.2rem_2.8rem_1.2rem_2rem]">
					{varType App\Model\Orm\Order\Class\ClassItem $classItem}
					{var $product = $classItem->getProduct()}
					{var $variant = $product->firstVariant}
					{var $link = $presenter->link($product, ['v' => $variant->id])}

					<td>
						<a href="{$link}" class="tw-relative">
							{if $variant && $variant->firstImage}
								<img class="img img--4-3 tw-rounded-lg" src="{$variant->firstImage->getSize('sm')->src}" alt="" loading="lazy">
							{else}
								<img class="img img--4-3 tw-rounded-lg" src="/static/img/illust/noimg.svg" alt="" loading="lazy"/>
							{/if}
							{include $templates.'/part/core/type.latte', product: $product, class: 'flag--sm flag--type'}
						</a>
					</td>
					<td class="max-lg:tw-col-start-2 max-lg:tw-col-end-4">
						<a href="{$link}" class="tw-font-bold tw-block">{$classItem->getName()}</a>
						<span n:ifcontent class="tw-text-help md:tw-text-[1.4rem]">
							{$classItem->getDesc()}
						</span>
					</td>
					<td class="max-lg:tw-mt-[0.8rem] max-lg:tw-col-start-1 max-lg:tw-col-end-4 md:tw-text-center max-lg:tw-flex tw-items-center tw-justify-between">
						<span class="tw-text-help md:tw-text-[1.2rem] tw-block md:tw-mb-[0.4rem]">{_"table_amount"}</span>
						{$classItem->amount} {_"stock_piece"}
					</td>
					<td class="max-lg:tw-col-start-1 max-lg:tw-col-end-4 md:tw-text-center max-lg:tw-flex tw-items-center tw-justify-between">
						<span class="tw-text-help md:tw-text-[1.2rem] tw-block md:tw-mb-[0.4rem]">{_"table_price_per_unit"}</span>
						{* {$classItem->unitPrice|money} *} {* TODO BE: formát je price misto money *}
					</td>
					<td class="max-lg:tw-mb-[0.8rem] max-lg:tw-col-start-1 max-lg:tw-col-end-4 md:tw-text-center max-lg:tw-flex tw-items-center tw-justify-between">
						<span class="tw-text-help md:tw-text-[1.2rem] tw-block md:tw-mb-[0.4rem]">{_"table_total_price"}</span>
						<b>{$classItem->totalPrice|money}</b>
					</td>
					<td class="max-md:tw-col-start-1 max-lg:tw-col-end-4 tw-text-right">
						{* TODO BE claims
						<a href="#" class="btn btn--bd btn--sm">
							<span class="btn__text">
								{_"btn_claims"}
							</span>
						</a>
						*}
					</td>
				</tr>
			</tbody>
		</table>
	{/if}
</div>
