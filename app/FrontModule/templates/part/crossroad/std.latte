{default $class = 'u-mb-sm u-mb-lg@md'}
{default $crossroad = $object->crossroad ?? []}
{default $items = []}
{default $type = "auto"}
{default $title = false}
{default $renderVirtual = false}

<section n:ifcontent n:class="c-categories, $class" data-controller="toggle-class">
	<p n:if="$title" class="tw-mb-[1.2rem] u-fw-b">{$title}</p>
	{define #img}
		{if $img}
			<img class="c-categories__img img img--contain img--4-3" src="{$img->getSize('sm')->src}" alt="" loading="lazy">
		{else}
			<img class="c-categories__img img img--contain img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	{/define}

	<ul n:ifcontent class="c-categories__list">
		{if $type == 'auto'}
			{* Automatický výpis (podstránky) *}
			{foreach $crossroad as $c}
				{continueIf $c->hideInMenu}
				{php $bg = $c->cf->settings->highlight ?? false}
				<li class="c-categories__item">
					{var $routable = $c}
					{if $renderVirtual}
						{var $virtualLink = App\Model\Link\VirtualLink::fromTreeEntity($routable)}
					{/if}

					{if $virtualLink ?? false}
						{if $virtualLink->page !== null}
							{capture $link}{plink $virtualLink->page}{/capture}
						{else}
							{capture $link}{$virtualLink->href}{/capture}
						{/if}
						{var $nameAnchor = $virtualLink->hrefName}
					{else}
						{capture $link}{plink $routable}{/capture}
						{var $nameAnchor = $c->nameAnchor}
					{/if}

					<a href="{$link}" n:class="c-categories__link, $bg">
						{php $img = isset($c->cf->base->crossroadImage) ? $c->cf->base->crossroadImage->getEntity() ?? false : false}
						{include #img, img: $img}
						{$nameAnchor}
					</a>
				</li>
			{/foreach}
		{elseif $type == 'rental'}
			{foreach $crossroad as $c}
				{capture $link}{plink $c}{/capture}
				<li class="c-categories__item">
					<a href="{$link}" n:class="c-categories__link">
						{php $img = isset($c->cf->base->crossroadImage) ? $c->cf->base->crossroadImage->getEntity() ?? false : false}
						{include #img, img: $img}
						<span>
							{$c->nameAnchor}
							<span class="tw-font-normal tw-text-help">(13)</span> {* TODO BE: count *}
						</span>
					</a>
				</li>
			{/foreach}
		{else}
			{* Custom výpis (CF) *}

			{foreach $items as $c}
				<li class="c-categories__item">
					{php $type = $c->link->toggle}
					{php $page = isset($c->link->systemHref) && isset($c->link->systemHref->page) ? $c->link->systemHref->page->getEntity() ?? false : false}
					{php $href = $c->link->customHref??->href ?? false}
					{php $hrefNameSystem = $c->link->systemHref??->hrefName ?? false}
					{php $hrefNameCustom = $c->link->customHref??->hrefName ?? false}
					{php $imgOverride = isset($c->image) ? $c->image->getEntity() ?? false : false}
					{php $bg = $c->highlight ?? false}

					{if $type == 'systemHref' && $page}
						{if isset($filter)}
							{php $cleanFilterParamCopy = null}
							{capture $link}{plink $page, filter => $cleanFilterParamCopy, 'pager-page' => null}{/capture}
							{php $link = urldecode(htmlspecialchars_decode($link))}
						{else}
							{capture $link}{plink $page}{/capture}
							{php $link = urldecode(htmlspecialchars_decode($link))}
						{/if}

						<a href="{$link}" n:ifcontent n:class="c-categories__link, $bg != 'none' ? $bg">
							{php $pageImg = isset($page->cf->base->crossroadImage) ? $page->cf->base->crossroadImage->getEntity() ?? false : false}
							{include #img, img: $imgOverride ? $imgOverride : $pageImg}

							{if $hrefNameSystem}
								{$hrefNameSystem}
							{else}
								{$page->nameAnchor}
							{/if}
						</a>
					{elseif $type == 'customHref' && $href && $hrefNameCustom}
						<a href="{$href}" n:class="c-categories__link, $bg != 'none' ? $bg" target="_blank" rel="noopener noreferrer">
							{include #img, img: $imgOverride}
							{$hrefNameCustom}
						</a>
					{/if}
				</li>
			{/foreach}
		{/if}
	</ul>
</section>
