{default $class = false}
{default $isBtn = false}
{default $linkChoose = false}
{default $icon = false}
{default $iconPosition = 'after'} {* before | after *}
{default $type = $linkChoose->toggle ?? 'customHref'}
{default $page = isset($linkChoose->systemHref) && isset($linkChoose->systemHref->page) ? $linkChoose->systemHref->page->getEntity() ?? false : false}
{default $hrefName = $type == 'systemHref' ? ($linkChoose->systemHref??->hrefName ?? false) : ($linkChoose->customHref??->hrefName ?? false)}
{default $customHref = $linkChoose->customHref??->href ?? false}
{default $href = $type == 'systemHref' && $page ? $presenter->link($page) : ($type == 'customHref' && $customHref ? $customHref : false)}
{default $text = $hrefName ?: ($page ? $page->nameAnchor : false)}
{default $blank = $type == 'customHref' && $customHref && !(strpos($customHref, '#') === 0)}
{default $attrs = []}
{default $customContent = false}

{* Add blank attributes for external custom hrefs *}
{if $blank}
	{php $attrs['target'] = '_blank'}
	{php $attrs['rel'] = 'noopener noreferrer'}
{/if}

<a n:if="$href" href="{$href}" n:ifcontent n:class="$class, $isBtn ? btn, $icon && !$isBtn ? item-icon" n:attr="$attrs">
	{if $customContent}
		{block content}{/block}
	{else}
		<span n:tag-if="$isBtn" class="btn__text">
			{if $icon && $iconPosition == 'before'}{($icon)|icon, $isBtn ? 'btn__icon' : 'item-icon__icon'}{/if}
			<span n:if="$text" n:tag-if="$icon && !$isBtn" class="item-icon__text">{$text}</span>
			{if $icon && $iconPosition == 'after'}{($icon)|icon, $isBtn ? 'btn__icon' : 'item-icon__icon'}{/if}
		</span>
	{/if}
</a>
