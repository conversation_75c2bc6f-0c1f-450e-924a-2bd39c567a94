{default $class = 'tw-mb-[3.2rem] md:tw-mb-[5.6rem]'}
{default $type = false}
{default $text = false}
{default $author = false}
{default $position = false}
{default $center = false}

{php $secondaryColor = false}
{switch $type}
	{case 'light-blue'}
		{php $class .= ' tw-rounded-[0.6rem] md:tw-rounded-[1.2rem] tw-p-[3.2rem] md:tw-p-[5.6rem] tw-bg-blue-10'}
		{php $secondaryColor = 'tw-text-medior-blue'}
	{case 'blue'}
		{php $class .= ' tw-rounded-[0.6rem] md:tw-rounded-[1.2rem] tw-p-[3.2rem] md:tw-p-[5.6rem] tw-bg-blue tw-text-white'}
	{default}
		{php $class .= ' tw-py-[3.2rem] md:tw-py-[5.6rem] tw-border-0 tw-border-solid tw-border-bd tw-border-t-[0.1rem] tw-border-b-[0.1rem]'}
		{php $secondaryColor = 'tw-text-medior-blue'}
{/switch}

<blockquote n:class="$class, 'u-mb-last-0', $center ? 'tw-text-center'">
	<div n:if="in_array($type, ['light-blue', 'blue'])" n:class="'tw-relative tw-inline-flex tw-font-tertiary tw-text-[7rem] md:tw-text-[10.4rem] tw-leading-[1] tw-size-[2.6rem] md:tw-size-[3.4rem] tw-mb-[1.6rem] md:tw-mb-[2.4rem]', $secondaryColor">
		<div class="tw-absolute tw-top-[-0.05em] tw-right-0 tw-flex tw-justify-end">&rdquo;</div>
	</div>
	<p n:if="$text" n:class="'tw-font-secondary tw-font-medium tw-text-[1.8rem] md:tw-text-[2.6rem] tw-leading-[1.5]', in_array($type, ['light-blue', 'blue']) ? 'tw-mb-[1.6rem] md:tw-mb-[2.4rem]' : 'tw-mb-[3.2rem] md:tw-mb-[5.6rem]'">
		{$text}
	</p>
	<p n:if="$author || $position" class="tw-text-[1.4rem] md:tw-text-[2rem]">
		<span n:if="$author" class="tw-block tw-font-secondary tw-font-medium">
			{$author}
		</span>
		<span n:if="$position" class="{$secondaryColor}">{$position}</span>
	</p>
</blockquote>
