{default $class = 'tw-mb-[3.2rem] md:tw-mb-[5.6rem]'}
{default $text = false}
{default $bg = 'light-blue'}

{php $secondaryColor = false}
{switch $bg}
	{case 'blue'}
		{php $class .= ' tw-bg-blue tw-text-white'}
	{default}
		{php $class .= ' tw-bg-blue-10'}
		{php $secondaryColor = 'tw-text-medior-blue'}
{/switch}

<p n:class="$class, 'md:tw-flex tw-items-center tw-gap-[3.2rem] tw-p-[3.2rem] tw-rounded-[0.6rem] md:tw-rounded-[1.2rem] tw-text-[1.8rem] md:tw-text-[2.6rem] tw-leading-[1.5] tw-font-secondary tw-font-medium max-md:tw-text-center'">
	<span n:class="'tw-font-tertiary tw-text-[4rem] md:tw-text-[8.5rem] tw-h-[4rem] md:tw-h-0 tw-flex tw-items-center tw-mb-[1.2rem] md:tw-mb-[-0.1em] max-md:tw-justify-center', $secondaryColor">!</span>
	{$text}
</p>
