{default $class = false}

<nav {*n:if="count($breadcrumbs) > 2"*} n:class="$class, 'tw-text-[1.2rem] tw-text-gray-50'">
	{define #bc}
		{default $class = false}
		<p class="tw-flex tw-flex-wrap tw-gap-[0.8rem] tw-mb-0">
			<strong class="u-vhide">
				{_breadcrumb_title}
			</strong>

			{foreach $breadcrumbs as $key=>$i}
				{if $i instanceof App\PostType\Page\Model\Orm\Tree}
					{var $nameAnchor = $i->getNameAnchorBreadcrumb()}
				{else}
					{var $nameAnchor = $i->nameAnchor}
				{/if}

				{if !$iterator->first && !$iterator->last}
					{('dash')|icon, 'tw-w-[1.2rem]'}
				{/if}
				{if $iterator->last}
					{if !$object instanceof App\Model\Orm\ProductLocalization\ProductLocalization}
						{('dash')|icon, 'tw-w-[1.2rem]'}<span class="tw-text-gray-50">{$nameAnchor}{if isset($_GET[search]) && $iterator->isLast() && $object->uid == 'search'}: {$_GET[search]}{/if}</span>
					{/if}
				{else}
					<a href="{plink $i, category: null, page: null}" class="tw-text-gray-50 tw-no-underline hover:tw-text-text">{$nameAnchor}</a>
				{/if}
			{/foreach}
		</p>
	{/define}

	{* {php $lastCategory = $breadcrumbs[count($breadcrumbs) - 2] ?? false}
	{if $lastCategory}
		{include #bc, class: 'u-d-n u-d-b@md'}
		<p class="m-breadcrumb__wrap u-mb-0 u-d-n@md">
			{('angle-left-bold')|icon, 'm-breadcrumb__separator'}
			<a href="{plink $lastCategory}" class="tw-text-gray-50 tw-no-underline hover:tw-text-text">{$lastCategory->nameAnchor}</a>
		</p>
	{else} *}
		{include #bc}
	{* {/if} *}
</nav>
