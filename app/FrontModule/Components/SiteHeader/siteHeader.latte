{varType App\Model\Pages $pages}

{php $isSimple = isset($object->uid) && in_array($object->uid, ['step1', 'step2'])}
{var $msgs = $systemMessages??[]} {* Nejstarší message *}
{* {php $catalogHeaderImage = isset($object->cf->catalog_header->image) ? $object->cf->catalog_header->image->getEntity() ?? false : false}
{php $headerImage = isset($object->cf->header->image) ? $object->cf->header->image->getEntity() ?? false : false}
{php $hasHeaderImage = $catalogHeaderImage || $headerImage} *}

<header class="header tw-pointer-events-auto tw-group/header tw-mb-[1.5rem] md:tw-mb-[3.5rem] tw-bg-white tw-border-0 tw-border-b-[0.1rem] tw-border-bd tw-border-solid tw-text-[1.4rem] tw-leading-[1.5]" data-controller="header etarget">
	{* {control currencyDetector} *}
	<div n:if="count($msgs)" class="header__msgs tw-mb-[-0.1rem] tw-relative sm:[.header:not(.is-menu-open):not(.header:has(.is-visible))_&]:tw-z-[2] group-[.is-menu-open]/header:tw-z-0 tw-bg-white">
		{foreach $msgs as $msg}
			{include './part/infobar.latte', msg=>$msg}
		{/foreach}
	</div>

	<div class="row-main tw-bg-white">
		<div class="{if $isSimple}xl:tw-grid-cols-[1fr_max-content_1fr]{else}xl:tw-pt-0 xl:tw-grid-cols-[auto_1fr_auto] xxl:tw-grid-cols-[1fr_max-content_1fr]{/if} max-xl:[.header:not(.header:has(.is-visible))_&]:tw-z-[1] tw-relative tw-p-[1.6rem_0] tw-grid tw-grid-cols-[1fr_auto_auto] tw-gap-[1.6rem_1.2rem] xl:tw-gap-[1.6rem_2.4rem] tw-items-center">
			<div class="{if !$isSimple}xl:tw-grid{/if} tw-contents header__top tw-pt-[1.2rem] tw-grid-cols-[auto_1fr_auto] xxl:tw-grid-cols-[1fr_59.2rem_1fr] xl:tw-gap-[1.6rem_2.4rem] tw-col-span-3 tw-relative tw-z-[1]
			sm:before:tw-content-[''] before:tw-pointer-events-none before:tw-absolute before:tw-top-0 before:tw-left-1/2 before:tw-w-[var(--vw)] before:tw-ml-[calc(var(--vw)/-2)] before:tw-bg-white before:tw-h-[var(--header-top-height)] before:[outline:500rem_solid_transparent] before:tw-transition-outline before:tw-duration-150
			">
				{* Kontakt *}
				{include $templates.'/part/box/person-header.latte', class: 'max-xl:tw-hidden', isSimple: $isSimple}

				{* Vyhledávání *}
				{if !$isSimple}
					{control suggest}
				{/if}

				{* Uživatel, oblíbené, košík *}
				<div n:class="$isSimple ? 'xl:tw-order-1', 'tw-flex tw-gap-[1.2rem] xl:tw-gap-[2.6rem] tw-justify-end'">
					{if !$isSimple}
						{include './part/login.latte', class: false}

						<p class="tw-mb-0 tw-font-secondary tw-font-medium sm:tw-z-[1]">
							<a href="{plink $pages->userSection}" n:class="'tw-h-full tw-flex tw-items-center tw-no-underline tw-gap-[0.4rem] max-xl:tw-p-[0_0.6rem] max-xl:tw-mx-[-0.6rem]'">
								{('heart')|icon, 'tw-w-[2.4rem]'}
								<span class="tw-flex-1 [text-box:trim-both] max-xl:tw-hidden">{_"header_favorite"}</span>
							</a>
						</p>
					{/if}

					{include './part/basket.latte', class: false}
				</div>
			</div>

			{* Logo *}
			<h1 n:tag="!$isHomepage ? 'p'" n:class="'tw-mt-0 tw-mb-0 tw-relative max-xl:tw-order-[-1]', $isHomepage ? 'tw-text-secondary tw-flex'">
				<a href="{plink $pages->title}" n:tag-if="!$isHomepage" class="tw-text-secondary hover:tw-text-secondary-hover" class="prefetch">
					{include $templates.'/part/core/logo.latte', class: 'tw-w-[9rem] xs:tw-w-[12.8rem]'}
				</a>
			</h1>

			{* Menu *}
			<nav n:if="!$isSimple" class="tw-self-stretch max-xl:tw-relative" id="menu-main" data-header-target="holder">
				<div class="tw-h-full tw-flex-col tw-inset-0 tw-bg-white tw-z-[3] tw-transition-[opacity,visibility] tw-duration-150 group-[.is-menu-open]/header:tw-opacity-[1] group-[.is-menu-open]/header:tw-visible
					max-xl:tw-flex max-xl:tw-p-[2.6rem_var(--row-main-gutter)] max-xl:tw-fixed max-xl:tw-opacity-0 max-xl:tw-invisible">
					<p class="h3 tw-text-[2rem] tw-flex tw-justify-between tw-items-center tw-mb-0 xl:tw-hidden">
						{_"menu_title_mobile"}
						<button type="button" class="as-link" data-action="header#toggleMenu">
							{('close-thin')|icon, 'tw-w-[2.4rem]'}
							<span class="u-vhide">{_"btn_close"}</span>
						</button>
					</p>

					{var $cacheObject = ($object::class === App\PostType\Page\Model\Orm\CatalogTree::class) ? $object : 'default'}
					{cache cacheKey('mainMenu', $cacheObject, $mutation), tags: [App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization::class, App\PostType\Page\Model\Orm\Tree::class]}
						{control menu}
					{/cache}
				</div>
				<p class="tw-mb-0 xl:tw-hidden tw-h-full">
					<button type="button" class="tw-h-full as-link tw-no-underline tw-flex tw-items-center tw-gap-[0.4rem] tw-font-medium tw-text-[1.2rem]" data-action="header#toggleMenu" aria-expanded="false" data-header-target="menuToggle">
						{('menu')|icon}
						{_"menu"}
					</button>
				</p>
			</nav>

			{* Doprava zdarma *}
			<p n:if="!$isSimple" class="tw-mb-0 tw-flex tw-gap-[0.6rem] tw-items-center tw-justify-end max-xl:tw-hidden">
				{('truck')|icon, 'tw-w-[2rem]'}
				{_"header_delivery_text"}
			</p>
		</div>
	</div>
</header>


{* {cache cacheKey('serviceMenu', $mutation), expire: '12 hour', tags: ['serviceMenu']}
	{control serviceMenu}
{/cache} *}
