{varType App\Model\Pages $pages}
{default $class = false}


{if $pages->precart !== null}
	{var App\Model\ShoppingCart\ShoppingCart $shoppingCart = $presenter->shoppingCart}

	{php $totalCount = $shoppingCart->getTotalCount()}

	{embed $templates.'/part/box/menu.latte', class: $totalCount > 0 ? 'b-menu--basket' : 'b-menu', shoppingCart: $shoppingCart}
		{block btn}
			<p class="tw-mb-0 tw-font-secondary tw-font-medium tw-h-full">
				<a href="{plink $pages->cart}" class="tw-flex tw-gap-[0.4rem] tw-items-center tw-justify-center tw-no-underline tw-h-full tw-bg-blue-10 tw-rounded-[0.6rem] max-xl:tw-size-[3.8rem] xl:tw-p-[0_1.8rem_0_0.8rem]" data-action="touch-open#open" aria-expanded="false">
					<span class="tw-relative tw-flex-[0_0_auto] tw-w-[2.9rem] tw-p-[0.3rem_0.5rem_0.5rem_0]">
						{('basket')|icon, 'tw-w-[2.4rem]'}
						<b class="tw-absolute tw-right-0 tw-bottom-0 tw-rounded-full tw-bg-green tw-w-[1.6rem] tw-aspect-square tw-flex tw-items-center tw-justify-center tw-text-white tw-text-[1.2rem] tw-font-secondary tw-font-medium tw-leading-none">{$totalCount}</b>
					</span>
					<span class="tw-font-secondary tw-font-medium max-xl:tw-hidden">
						{_"shopping_basket"}
					</span>
				</a>
			</p>
		{/block}
		{block content}
			{control cart:header}
		{/block}
	{/embed}
{/if}
