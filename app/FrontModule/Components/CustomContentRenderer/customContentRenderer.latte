
{var $props = [
	templateDirectory: (isset($props['templateDirectory'])) ? $props['templateDirectory'] : $defaultTemplateDirectory,
	customContent: (isset($props['customContent'])) ? $props['customContent'] : $defaultObjectCC,
]}

{if !Nette\Utils\Strings::endsWith($props['templateDirectory'], '/')}
	{var $props['templateDirectory'] = $props['templateDirectory'].'/'}
{/if}


{php $templateCounters = []}
{foreach $props['customContent'] as $key=>$item}
	{var $item = $item[0]} {*remove first level of group*}
	{var $templateName = substr($key, 0, strpos($key, '____'))}
	{var $niceName = ($allCustomComponentsLabels[$templateName] ?? null) ?? $templateName}

	{if !isset($templateCounters[$templateName])}
		{php $templateCounters[$templateName] = 0}
	{else}
		{php $templateCounters[$templateName]++}
	{/if}

	<div n:tag-if="$isDemo" class="component u-mb-last-0">
		<strong n:if="$isDemo" class="component__title u-font-label">{$niceName}</strong>

		{if $isDev}
			{include $props['templateDirectory'].$templateName.'.latte', customContentItem=>$item, parentIterator=>$iterator}
		{else}
			{try}
				{include $props['templateDirectory'].$templateName.'.latte', customContentItem=>$item, parentIterator=>$iterator}
			{/try}
		{/if}
	</div>
{/foreach}
