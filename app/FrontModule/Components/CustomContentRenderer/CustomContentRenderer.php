<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer;

use App\Infrastructure\Latte\Filters;
use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\IEntity;
use App\Model\CustomContent\CustomContent;

/**
 * @property-read DefaultTemplate $template
 */
final class CustomContentRenderer extends UI\Control
{

	public function __construct(
		private readonly IEntity $object,
		private readonly Mutation $mutation,
		private readonly PriceLevel $priceLevel,
		private readonly State $state,
		private readonly TranslatorDB $translator,
		private readonly ConfigService $configService,
		private readonly CustomContent $customContent,
	)
	{
	}


	public function render(array $props = []): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->templates = FE_TEMPLATE_DIR;
		$template->defaultTemplateDirectory = FE_TEMPLATE_DIR . '/part/customContent';
		$template->isDev = $this->configService->get('isDev');
		$template->props = $props;
		$template->object = $this->object;
		$template->mutation = $this->mutation;
		$template->pages = $this->mutation->pages;
		$template->priceLevel = $this->priceLevel;
		$template->state = $this->state;
		assert(method_exists($this->object, 'getCcModules'));
		if ($this->object->getCcModules()) {
			assert(isset($this->object->cc));
			$template->defaultObjectCC = $this->object->cc ?? [];
		} else {
			$template->defaultObjectCC = [];
		}

		$isDemo = (isset($this->object->parent->uid) && $this->object->parent->uid == "ccDemo") || (isset($this->object->uid) && $this->object->uid == 'ccDemo');
		$this->template->isDemo = $isDemo;
		if ($isDemo) {
			$this->template->allCustomComponentsLabels = $this->customContent->getAllCustomComponentsLabels((array) $this->object->getCcModules());
		} else {
			$this->template->allCustomComponentsLabels = [];

		}

		$template->addFilter('parseVideoId', Filters::parseVideoId(...));
		$template->render(__DIR__ . '/customContentRenderer.latte');
	}

}
