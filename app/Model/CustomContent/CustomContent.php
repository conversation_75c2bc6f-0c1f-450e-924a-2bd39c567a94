<?php

declare(strict_types=1);

namespace App\Model\CustomContent;

use App\Model\CustomField\LazyValueFactory;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\User\User;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use Nextras\Orm\Entity\IEntity;
use Throwable;
use Tracy\Debugger;

final class CustomContent
{
	/**
	 * @param array<string, CustomComponent[]> $templateMapping
	 */
	public function __construct(
		private array $templateMapping,
		private Orm $orm,
		private LazyValueFactory $lazyValueFactory,
	) {}

	public function prepareDataToSave(string $customContent, string $customContentScheme): ArrayHash
	{
		if ($customContent === '') {
			return new ArrayHash();
		}

		$customContent = Json::decode($customContent);
		$customContentScheme = Json::decode($customContentScheme, forceArrays: true);

		$schemeKeys = [];
		foreach ($customContentScheme as $key => $item) {
			if (\array_key_exists('order', $item)) {
				$schemeKeys[$item['order']] = $key;
			} else {
				$schemeKeys[] = $key;
			}
		}

		\ksort($schemeKeys);

		$newCustomContent = new ArrayHash();
		foreach ($schemeKeys as $schemeKey) {
			if (isset($customContent->$schemeKey)) {
				$newCustomContent->$schemeKey = $customContent->$schemeKey;
			}
		}

		return $newCustomContent;
	}

		/**
	 * @return array<string, string>
	 */
	public function getAllCustomComponentsLabels(array $customContentScheme): array
	{
		$labels = [];


		foreach ($customContentScheme as $component) {
			$labels[(string)$component->template] = (string)$component->definition->label;
		}

		return $labels;
	}


	public function prepareForToShow(IEntity $object, \stdClass $data, bool $inRs = false): \stdClass
	{
		// @phpstan-ignore-next-line
		$scheme = $object->getCcScheme();
		$dataToShow = new \stdClass();
		// @phpstan-ignore-next-line
		foreach ($data as $key => $value) {

			if (isset($scheme[$key])) {

				$customFields = $scheme[$key]['items'];

				$result = new \stdClass();
				foreach ($value[0] as $fieldName => $fieldValue) {
					$fieldResult = \array_key_exists($fieldName, $customFields)
						? $customFields[$fieldName]->process($fieldValue, $inRs, $this->orm, $this->lazyValueFactory)
						: null;

					if ($fieldResult !== null) {
						$result->$fieldName = $fieldResult;
					}
				}

				$dataToShow->$key = [];
				$dataToShow->$key[0] = $result;
			}
		}

		return $dataToShow;
	}

	public function prepareForToShowInRs(IEntity $object, \stdClass $data): \stdClass
	{
		return $this->prepareForToShow($object, $data, inRs: true);
	}

	/**
	 * @return CustomComponent[]
	 */
	public function resolveCustomComponentsFor(IEntity $object): array
	{
		$possibleTemplates = $this->findPossibleTemplatesFor($object);
		foreach ($possibleTemplates as $possibleTemplate) {
			if (\array_key_exists($possibleTemplate, $this->templateMapping)) {
				return $this->templateMapping[$possibleTemplate];
			}
		}

		return [];
	}

	/**
	 * @return string[]
	 */
	private function findPossibleTemplatesFor(IEntity $object): array
	{
		$objectTemplates = [];
		$className = \get_class($object);
		$pos = \strrpos($className, '\\');
		if ($pos !== false) {
			$cleanClassName = Strings::firstLower(\substr($className, $pos + 1));
		} else {
			$cleanClassName = Strings::firstLower($className);
		}

		if (isset($object->uid)) {
			$objectTemplates[] = 'uid-' . $object->uid;
		}

		if ($object instanceof User) {
			$objectTemplates[] = 'user-' . $object->id;
			$objectTemplates[] = 'user-' . $object->role;
			$objectTemplates[] = 'users';
		} elseif ($object instanceof Parameter) {
			$objectTemplates[] = 'parameter-' . $object->uid;
			$objectTemplates[] = 'parameters';
		} elseif ($object instanceof ProductLocalization) {
			$objectTemplates[] = $object->product->template;
		} elseif (isset($object->template) && isset($this->templateMapping[$object->template])) {
			$objectTemplates[] = $object->template;
		} elseif (isset($this->templateMapping[$cleanClassName])) {
			$objectTemplates[] = $cleanClassName;
		}

		return $objectTemplates;
	}
}
