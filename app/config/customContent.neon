extensions:
	cc: App\Model\CustomContent\CustomContentExtension

cc:
	definitions:
		content:
			type: group
			label: "Obsah"
			items:
				content: @cf.definitions.content
		quote:
			type: group
			label: "Citace"
			items:
				type:
					type: select
					label: "Barva pozadí"
					defaultValue: "none"
					options: [
						{ label: "<PERSON><PERSON><PERSON><PERSON> (defaultní)", value: "none" },
						{ label: "Světle modré", value: "light-blue" },
						{ label: "Modré", value: "blue" },
					]
				text:
					type: textarea
					label: "Obsah"
				author:
					type: text
					label: "Autor"
				position:
					type: text
					label: "Pozice"
				center:
					type: checkbox
					label: "Zarovnání na střed"
					defaultValue: false

		info_box:
			type: group
			label: "Box s upozorněním"
			items:
				text:
					type: textarea
					label: "Text"
				bg:
					type: select
					label: "Barva pozadí"
					defaultValue: "light-blue"
					options: [
						{ label: "Modrá", value: "blue" },
						{ label: "<PERSON>větle modrá (defaultní)", value: "light-blue" },
					]

		zigzag:
			type: group
			label: "Zigzag"
			items:
				items:
					type: list
					items:
						image:
							type: image
							label: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (min. 830px)"
						content:
							type: tinymce
							label: "<PERSON><PERSON><PERSON>"

		gallery:
			type: group
			label: "Fotogalerie"
			items:
				images:
					type: image # has size
					label: "Fotografie"
					multiple: true
				type:
					type: select
					label: "Typ zobrazení"
					defaultValue: "carousel"
					options: [
						{ label: "Carousel (defaultní)", value: "carousel" },
						{ label: "Mřížka", value: "grid" },
					]

		newsletter:
			type: group
			label: "Newsletter"
			items:
				title:
					type: text
					label: "Nadpis"
					defaultValue: "Chceš mít péči pod kontrolou?"
				annot:
					type: text
					label: "Popisek"
					defaultValue: "Více tipů přímo do tvojí schránky."

		ears:
			type: group
			label: "Nastraž uši"
			items:
				text:
					type: textarea
					label: "Obsah"
				bg:
					type: select
					label: "Barva pozadí"
					defaultValue: "light-blue"
					options: [
						{ label: "Modrá", value: "blue" },
						{ label: "Světle modrá (defaultní)", value: "light-blue" },
					]

		bnrs:
			type: group
			label: "Bannery s ikonou"
			items:
				items:
					type: list
					label: "Položky"
					items:
						link:
							extends: @cf.definitions.linkChooseNoText
							label: "Odkaz"
						icon:
							type: image
							label: "SVG ikona"
						title:
							type: text
							label: "Nadpis"
						desc:
							type: text
							label: "Popis"
						color:
							type: select
							label: "Barva"
							defaultValue: "blue"
							options: [
								{ label: "Modrá (defaultní)", value: "blue" },
								{ label: "Zelená", value: "green" },
								{ label: "Hnědá", value: "brown" },
							]


		# video:
		# 	type: group
		# 	label: "Video"
		# 	items:
		# 		link:
		# 			type: text
		# 			label: "Odkaz na Youtube / Vimeo"
		# 		poster:
		# 			type: image # has size
		# 			label: "Zástupný obrázek (min.1540x1540)"

	components:
		# Obsah
		content:
			icon: "paragraph"
			template: "content"
			category: "Obsah"
			definition: @cc.definitions.content
			hotkey: true
		quote:
			icon: "quote-right"
			template: "quote"
			category: "Obsah"
			definition: @cc.definitions.quote
		info_box:
			icon: "exclamation"
			template: "info_box"
			category: "Obsah"
			definition: @cc.definitions.info_box
		zigzag:
			icon: "columns"
			template: "zigzag"
			category: "Obsah"
			definition: @cc.definitions.zigzag
		ears:
			icon: "lightbulb"
			template: "ears"
			category: "Obsah"
			definition: @cc.definitions.ears

		# Media
		gallery:
			icon: "image"
			template: "gallery"
			category: "Média"
			definition: @cc.definitions.gallery

		# Ostatní
		newsletter:
			icon: "envelope"
			template: "newsletter"
			category: "Ostatní"
			definition: @cc.definitions.newsletter
		bnrs:
			icon: "adversal"
			template: "bnrs"
			category: "Ostatní"
			definition: @cc.definitions.bnrs

	templates:
		":Front:Page:default": *
		# ":Front:Page:references": *
		# ":Front:Page:sustainable": *
		# ":Front:Page:insurance": *
		# ":Front:Page:media": *
		# ":Front:Page:career": *
		# ":Front:Page:careerDetail": * # TODO vymenit za postType sablonu
		# ":Front:Page:investment": *
		# ":Front:Page:contact": *
		# ":Front:Page:academy": *
		# ":Front:Page:ebook": * # TODO vymenit za postType sablonu
		# ":Front:Page:termDetail": * # TODO vymenit za postType sablonu
		# ":Front:Page:about": *
		# ":Front:Page:store": *
		# ":Front:Page:filmingShooting": *
		# ":Front:Page:deliveryAndPayment": *
		# ":Front:Page:buyin": *
		# ":Front:Page:rental": *
		# ":Front:Page:service": *
		# ":Front:Page:rentalStep1": *
		# ":Front:Page:rentalStep2": *
		# ":Front:Page:rentalStep3": *
		# ":Blog:Front:Blog:default": *
		# ":Blog:Front:Blog:detail": *
		# ":Banner:Front:Banner:detail": *


services:
	-
		implement: App\FrontModule\Components\CustomContentRenderer\CustomContentRendererFactory
		inject: true
