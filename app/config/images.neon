parameters:
	noImageFile: '/static/img/illust/noimg.svg'
	mainImageDirectory: '/data/images'
	config:
		imageOriginal:
			resize: TRUE
			width:  3840
			height: 3840

		# quality: [0–100]; defaults to 85 (JPG), 80 (WEBP), 30 (AVIF), 100 (PNG)
		imageSizes:
			# SA - part/head/meta.latte && defail noImageSiez for SVG/GIF
			ogImage:
				width:  1200
				height: 630
				keepRatio: true

			max:
				width: 3840
				height: 3840

			2xl:
				width:  1920
				height: 1920
			2xl-2-1:
				width:  1920
				height: 960
				keepRatio: true
			2xl-3-2:
				width:  1920
				height: 1280
				keepRatio: true

			xl:
				width:  1400
				height: 1400
			xl-2-1:
				width:  1400
				height: 700
				keepRatio: true
			xl-3-2:
				width:  1400
				height: 934
				keepRatio: true

			lg:
				width: 750
				height: 750
			lg-2-1:
				width:  750
				height: 375
				keepRatio: true
			lg-3-2:
				width:  750
				height: 500
				keepRatio: true

			md:
				width:  560
				height: 560
			md-2-1:
				width:  560
				height: 280
				keepRatio: true
			md-3-2:
				width:  560
				height: 374
				keepRatio: true

			sm:
				width:  320
				height: 320

			xs:
				width:  100
				height: 100

			# SA - don't delete
			library:
				width:  1800
				height: 1800
			s:
				width:  280
				height: 280
			l:
				width:  800
				height: 800





services:
	- App\Model\Image\Storage\BasicStorage(%config.imageSizes%, %config.WWW_DIR%, mainImageDirectory: %mainImageDirectory%, forcedDomain: %config.imageFromStage%)
	- App\Model\Image\Resizer(%config.WWW_DIR%)
	- App\Model\Image\Setup\ImageSizes(%config.imageSizes%)
	- App\Model\Image\ImageObjectFactory(noImageFile: %noImageFile%, wwwDir: %config.WWW_DIR%)
	- App\Model\Image\BetterImageTypeDetector
	- App\Model\Image\Rotator

