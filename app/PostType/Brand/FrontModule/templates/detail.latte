{varType App\PostType\Brand\Model\Orm\Brand $object}

{block content}
	<div class="row-main">


		{snippet catalogHeader}
			{include $templates.'/part/box/header.latte', class: 'b-header--catalog u-mb-xs u-mb-md@md', showMore: true, cf: $object->cf->catalog_header ?? false, disableDescriprion: $filter->nonRoot}
		{/snippet}

		<div class="b-catalog u-mb-md u-mb-xl@md">
			{* Filter, sort, selected *}
			{*{snippetArea filterAreaMainWrapper}
				{include $templates.'/part/box/filter.latte', class: false}
			{/snippetArea}*}
			<div class="b-catalog__main">
				{snippet products}
					{control catalogProducts}
				{/snippet}
			</div>
		</div>

		{control customContentRenderer}
	</div>
{/block}


{*
{block content}

	<div class="u-pt-lg">
		<div class="row-main">
			{control breadcrumb}

			{include $templates.'/part/box/annot.latte'}

			{include $templates.'/part/crossroad/brand.latte', crossroad=>$randomBrands, button=>true, customTitle=>"title_brands_other", class=>'u-mb-lg u-mb-xl@lg'}
		</div>
	</div>


{/block}
*}
