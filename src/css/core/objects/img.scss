@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.img {
	$s: &;
	display: block;
	width: 100%;
	height: fit-content;
	aspect-ratio: 1/1;
	object-fit: cover;

	// MODIF
	&--3-2 {
		aspect-ratio: 3/2;
	}
	&--2-1 {
		aspect-ratio: 2/1;
	}
	&--circle {
		border-radius: 50%;
	}
	&--contain {
		object-fit: contain;
	}
	&--fit {
		width: 100%;
		height: 100%;
	}
}
