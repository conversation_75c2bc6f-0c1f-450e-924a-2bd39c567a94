@use 'config';
@use 'base/variables';

html {
	color: variables.$color-text;
	font-size: 62.5%;
}

body {
	font-family: variables.$font-primary;
	font-size: variables.$font-size;
	line-height: variables.$line-height;
}

// Headings
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
	margin: 1.5em 0 0.5em;
	font-family: variables.$font-secondary;
	font-weight: 570;
	font-size: var(--font-size-mobile);
	line-height: 1.2;
	letter-spacing: var(--letter-spacing-mobile);
	@media (config.$md-up) {
		font-size: var(--font-size-desktop);
		letter-spacing: var(--letter-spacing-desktop);
	}
}
h1,
.h1 {
	--font-size-mobile: 2.2rem;
	--font-size-desktop: 3.2rem;
	--letter-spacing-mobile: -0.03rem;
	--letter-spacing-desktop: -0.06rem;
}
h2,
.h2 {
	--font-size-mobile: 1.8rem;
	--font-size-desktop: 2.8rem;
	--letter-spacing-mobile: -0.01rem;
	--letter-spacing-desktop: -0.06rem;
}
h3,
.h3 {
	--font-size-mobile: 1.4rem;
	--font-size-desktop: 2rem;
}
h4,
.h4 {
	--font-size-mobile: 1.4rem;
	--font-size-desktop: 1.8rem;
}
h5,
.h5 {
	--font-size-mobile: 1.4rem;
	--font-size-desktop: 1.6rem;
}
h6,
.h6 {
	--font-size-mobile: 1.4rem;
	--font-size-desktop: 1.4rem;
}

// Paragraph
p {
	margin: 0 0 variables.$typo-space-vertical;
}
hr {
	height: 0.1rem;
	margin: variables.$typo-space-vertical 0;
	border: solid variables.$color-bd;
	border-width: 0.1rem 0 0;
	overflow: hidden;
}

// Blockquote
blockquote {
	margin: 0 0 variables.$typo-space-vertical;
	padding: 0;
	& > *:not(.grid):last-child {
		margin-bottom: 0;
	}
}

// Links
a,
.as-link {
	--color-link: #{variables.$color-text};
	--color-hover: #{variables.$color-primary};
	--color-link-decoration: var(--color-link);
	--color-hover-decoration: var(--color-hover);
	color: var(--color-link);
	text-decoration: underline;
	text-decoration-color: var(--color-link-decoration);
	transition: color variables.$t, text-decoration-color variables.$t;
	-webkit-tap-highlight-color: transparent;
	.hoverevents &:hover {
		color: var(--color-hover);
		text-decoration-color: var(--color-hover-decoration);
	}
}

.as-link {
	cursor: pointer;
}

// Lists
*:is(ul, ol, dl) {
	margin: 0 0 variables.$typo-space-vertical;
	// padding: 0;
	// list-style: none;
	padding: 0 0 0 2.6rem;
}
li {
	// 	margin: 0 0 calc(variables.$typo-space-vertical / 4);
	// padding: 0 0 0 2rem;
}
// ul {
// 	li {
// 		background-image: url(variables.$svg-bullet);
// 		background-position: 0.5rem 0.5em;
// 		background-repeat: no-repeat;
// 		background-size: 0.4rem 0.4rem;
// 	}
// }
// ol {
// 	counter-reset: item;
// 	li {
// 		position: relative;
// 		&::before {
// 			content: counter(item) '.';
// 			counter-increment: item;
// 			position: absolute;
// 			top: 0;
// 			left: 0;
// 		}
// 	}
// 	ol {
// 		li {
// 			&::before {
// 				content: counter(item, lower-alpha) '.';
// 			}
// 		}
// 	}
// }
dt {
	margin: 0;
	font-weight: bold;
}
dd {
	margin: 0 0 calc(variables.$typo-space-vertical / 2);
	padding: 0;
}

// Tables
table {
	--table-x-padding: 1rem;
	--table-y-padding: 0.6rem;
	--table-bd-color: #{variables.$color-bd};
	clear: both;
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
	width: 100%;
	margin: 0 0 variables.$typo-space-vertical;
}
caption {
	padding: 0 0 1rem;
	font-weight: bold;
	text-align: left;
	caption-side: top;
}
*:is(td, th) {
	vertical-align: top;
	padding: var(--table-y-padding) var(--table-x-padding);
	border: 0.1rem solid var(--table-bd-color);
	border-width: 0.1rem 0;
	&:first-child {
		padding-left: 0;
	}
	&:last-child {
		padding-right: 0;
	}
}
th {
	font-weight: bold;
	text-align: left;
}
thead th {
	border-top: none;
}
tfoot th {
	border-bottom: none;
}
table:has(tbody tr:last-child) {
	tbody tr:last-child *:is(th, td) {
		border-bottom: none;
	}
}
caption + tbody tr:first-child *:is(th, td),
table:has(tbody tr:first-child) {
	tbody tr:first-child *:is(th, td) {
		border-top: none;
	}
}
// Image
figure {
	margin-bottom: variables.$typo-space-vertical;
}
figcaption {
	margin-top: 0.5em;
}

img {
	max-width: 100%;
	height: auto;
}
