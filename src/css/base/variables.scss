@use 'config';

// Colors
$color-black: #121212;
$color-dark-blue: #070f2a;
$color-medior-blue: #3e3e59;
$color-medior-blue-30: #3e3e594d;

$color-gray-50: #1e294f80;
$color-gray-20: #00176633;
$color-gray-10: #0017661a;
$color-gray: #c7cad3;
$color-disabled: $color-gray;

$color-blue: #3e8dff;
$color-blue-hover: #72acff;
$color-blue-10: #3e8dff1a;
$color-blue-20: #81b4fe;
$color-blue-30: #3e8dff4d;

$color-azure: #3296d5;
$color-cyan: #279fab;

$color-brown: #d29f7b;
$color-brown-hover: #ecbd9c;

$color-beige: #f9f5f2;
$color-rust: #bf522c;
$color-rust-10: #bf522c1a;
$color-rust-hover: #dc6d46;
$color-rust-hover-15: #dc6d4626;

$color-teal: #1ba97f;
$color-teal-hover: #2fc397;
$color-teal-10: #1ba97f1a;
$color-teal-20: #1ba97f33;
$color-white: #ffffff;
$color-red: $color-rust; // todo
$color-green: #45b26b;
$color-orange: #ffaa00; // todo
$color-yellow: #efca23;
$color-yellow-15: #efca2326;

$color-primary: $color-blue;
$color-secondary: $color-brown;
$color-primary-hover: $color-blue-hover;
$color-secondary-hover: $color-brown-hover;

$color-text: $color-dark-blue;
$color-bd: $color-gray-10;
$color-bg: $color-beige;
$color-link: $color-blue;
$color-hover: $color-blue-hover;

// Font
$font-system: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, helvetica, arial, sans-serif;
$font-primary: 'Inter', $font-system;
$font-secondary: 'Haffer', $font-primary;
$font-tertiary: 'Pumped Up Kicks', $font-primary;
$font-size: var(--font-size);
$line-height: calc(26 / 16);

// Typography
$typo-space-vertical: 1.6em;

// Focus
$focus-outline-color: $color-primary;
$focus-outline-style: solid;
$focus-outline-width: 0.2rem;
$focus-outline-offset: 0.2rem;

// Spacing - TODO
// $utils-spacing: (
// 	'0': 0,
// 	'xs': 1rem,
// 	'sm': 2rem,
// 	'md': 4rem,
// 	'lg': 6rem,
// 	'xl': 8rem,
// 	'2xl': 10rem
// );

// Grid
$grid-columns: 12;
$grid-gutter: var(--grid-gutter);
$row-main-width: var(--row-main-width);
$row-main-gutter: var(--row-main-gutter);

// Paths
$img-path: map-get(config.$paths, 'images');
$fonts-path: map-get(config.$paths, 'fonts');

// Transitions
$t: 0.15s;

// SVGs
$svg-bullet: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath d='M0 0h4v4H0z'/%3E%3C/svg%3E%0A";
$svg-select: 'data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2020%2020%22%3E%3Cpath%20fill%3D%22%23070f2a%22%20d%3D%22m15%206.6-5%205-5-5-1.06%201.06%205.53%205.53a.75.75%200%200%200%201.06%200l5.53-5.53L15%206.59Z%22%2F%3E%3C%2Fsvg%3E%0A';

:root {
	--grid-gutter: 1.2rem;
	--row-main-gutter: 1.6rem;
	--row-main-width: calc(180rem + 2 * var(--row-main-gutter));
	--color-black: #{$color-black};
	--color-dark-blue: #{$color-dark-blue};
	--color-medior-blue: #{$color-medior-blue};
	--color-medior-blue-30: #{$color-medior-blue-30};
	--color-white: #{$color-white};
	--color-red: #{$color-red};
	--color-green: #{$color-green};
	--color-orange: #{$color-orange};
	--color-gray-50: #{$color-gray-50};
	--color-gray-20: #{$color-gray-20};
	--color-gray-10: #{$color-gray-10};
	--color-blue-10: #{$color-blue-10};
	--color-blue-30: #{$color-blue-30};
	--color-blue-20: #{$color-blue-20};
	--color-disabled: #{$color-disabled};
	--color-beige: #{$color-beige};
	--color-rust: #{$color-rust};
	--color-rust-10: #{$color-rust-10};
	--color-rust-hover: #{$color-rust-hover};
	--color-gray: #{$color-gray};
	--color-text: #{$color-text};
	--color-bd: #{$color-bd};
	--color-bg: #{$color-bg};
	--color-blue: #{$color-blue};
	--color-blue-hover: #{$color-blue-hover};
	--color-brown: #{$color-brown};
	--color-brown-hover: #{$color-brown-hover};
	--color-primary: #{$color-primary};
	--color-secondary: #{$color-secondary};
	--color-primary-hover: #{$color-primary-hover};
	--color-secondary-hover: #{$color-secondary-hover};
	--color-teal-10: #{$color-teal-10};
	--color-teal-20: #{$color-teal-20};
	--color-rust-hover-15: #{$color-rust-hover-15};
	--font-primary: #{$font-primary};
	--font-secondary: #{$font-secondary};
	--font-tertiary: #{$font-tertiary};
	--color-link: #{$color-primary};
	--color-hover: #{$color-secondary};
	--color-teal: #{$color-teal};
	--color-azure: #{$color-azure};
	--color-cyan: #{$color-cyan};
	--color-teal-hover: #{$color-teal-hover};
	--color-yellow: #{$color-yellow};
	--color-yellow-15: #{$color-yellow-15};
	--font-size: 1.6rem;
	--vw: calc(100vw - var(--scrollbar-width, 0rem));
	--header-top-height: 6.9rem;
	--header-height: 12rem;

	// Spacings for Tailwind
	// @each $key, $value in $utils-spacing {
	// 	--spacing-#{$key}: #{$value};
	// }

	// MQ
	@media (config.$md-up) {
		--row-main-gutter: 2rem;
	}
	@media (config.$xl-up) {
		--header-top-height: 6.2rem;
	}
	@media (config.$xxxl-up) {
		--row-main-gutter: 6rem;
	}
}
