/* stylelint-disable selector-id-pattern */
@use 'base/variables';

#snippet--header {
	position: sticky;
	top: 0;
	z-index: 10;
	transition: transform 0.3s;
	pointer-events: none;

	// MODIF
	// scroll dolů - schováí
	&.is-hidden,
	&.is-unpinned {
		transform: translateY(-100%);
	}
	&.is-pinned::before,
	&.is-unpinned::before {
		opacity: 0;
	}
	&:has(.header--fixed) {
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
	}
}
