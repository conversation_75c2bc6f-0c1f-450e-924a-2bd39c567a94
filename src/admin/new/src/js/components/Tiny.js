import { Controller } from 'stimulus';
import { useIntersection } from 'stimulus-use';
import { useDispatch } from 'stimulus-use';

export default class Tiny extends Controller {
	static targets = ['item'];
	intersectoinOptions = {
		rootMargin: '0px 0px 300px 0px',
	};

	editor = null;

	connect() {
		useDispatch(this);
		useIntersection(this, this.intersectoinOptions);
	}

	appear() {
		const tinyController = this;
		const tinyMceConfig = {
			entity_encoding: 'raw',
			remove_linebreaks: 'false',
			gecko_spellcheck: false,
			keep_styles: true,
			accessibility_focus: true,
			tabfocus_elements: 'major-publishing-actions',
			media_strict: false,
			paste_remove_styles: false,
			paste_remove_spans: false,
			paste_strip_class_attributes: 'none',
			paste_text_use_dialog: true,
			relative_urls: false,

			setup: function(editor) {
				// function skMsg() {
				// 	editor.insertContent(`
				// 		<div class="message message--md message--warning">
				// 			<div class="message__emoji">👌</div>
				// 			<div class="message__content u-mb-last-0">
				// 				<p><PERSON><PERSON><PERSON> obsah</p>
				// 			</div>
				// 		</div>
				// 		`);
				// }
				// editor.ui.registry.addButton('skMsg', {
				// 	text: 'Info lišta',
				// 	tooltip: 'Info lišta',
				// 	onAction: skMsg,
				// });

				// SK Button
				function skButtonWindow() {
					// Načti defaultní hodnoty
					var btn = editor.selection.getNode().closest('.btn');
					var link = btn ? btn.getAttribute('href') : '';
					var text = editor.selection.getNode().textContent;
					var blank = btn && btn.getAttribute('target') ? true : false;
					var type = btn?.classList?.[1] ?? '';
					// var size = btn?.classList?.[2] ?? '';

					editor.windowManager.open({
						title: 'Vložit tlačítko',
						body: {
							type: 'panel',
							items: [
								{
									type: 'input',
									name: 'link',
									label: 'URL',
								},
								{
									type: 'input',
									name: 'text',
									label: 'Text tlačítka',
								},
								{
									type: 'selectbox',
									name: 'type',
									label: 'Vyberte styl',
									items: [
										{ value: '', text: 'Modré (defaultní)' },
										// { value: 'btn--secondary', text: 'Sekundární' },
										// { value: 'btn--white', text: 'Bílé' },
										// { value: 'btn--shadow', text: 'Bílé se stínem' },
										{ value: 'btn--bd', text: 'S ohraničením' },
										// { value: 'btn--secondary btn--bd', text: 'Sekundární s ohraničením' },
									],
								},
								// {
								// 	type: 'selectbox',
								// 	name: 'size',
								// 	label: 'Vyberte velikost',
								// 	items: [
								// 		{ value: 'btn--sm', text: 'SM' },
								// 		{ value: '', text: 'MD (defaultní)' },
								// 		{ value: 'btn--lg', text: 'LG' },
								// 		{ value: 'btn--xl', text: 'XL' },
								// 	],
								// },
								{
									type: 'checkbox',
									name: 'blank',
									label: 'Otevřít odkaz v novém okně',
								},
							],
						},
						initialData: {
							link: link,
							text: text,
							blank: blank,
							type: type,
							// size: size,
						},
						buttons: [
							{
								type: 'cancel',
								text: 'Close',
							},
							{
								type: 'submit',
								text: 'Save',
								primary: true,
							},
						],
						onSubmit: function(api) {
							let data = api.getData();
							var newBtn = `<a href="${data.link}" class="btn btn--md ${data.type}" ${data.blank ? 'target="_blank"' : null}>
								<span class="btn__text"><span>${data.text}</span></span></a>`;

							// var newBtn = `<a href="${data.link}" class="btn ${data.type} ${data.size}" ${
							// 	data.blank ? 'target="_blank"' : null
							// }>
							// 	<span class="btn__text"><span>${data.text}</span></span></a>`;

							// Pokud tlačítko existuje, tak jej před vložením smažeme
							if (btn) editor.dom.remove(editor.selection.getNode().closest('.btn'));
							editor.insertContent(newBtn);
							api.close();
						},
					});
				}
				editor.ui.registry.addToggleButton('skButtonWindow', {
					text: 'Tlačítko',
					tooltip: 'Tlačítko',
					onAction: skButtonWindow,
					onSetup: (buttonApi) => editor.selection.selectorChangedWithUnbind('a.btn', buttonApi.setActive).unbind,
				});

				function skButtonWindowLite() {
					// Načti defaultní hodnoty
					var btn = editor.selection.getNode().closest('.btn');
					var link = btn ? btn.getAttribute('href') : '';
					var text = editor.selection.getNode().textContent;
					var blank = btn && btn.getAttribute('target') ? true : false;

					editor.windowManager.open({
						title: 'Vložit tlačítko',
						body: {
							type: 'panel',
							items: [
								{
									type: 'input',
									name: 'link',
									label: 'URL',
								},
								{
									type: 'input',
									name: 'text',
									label: 'Text tlačítka',
								},
								{
									type: 'checkbox',
									name: 'blank',
									label: 'Otevřít odkaz v novém okně',
								},
							],
						},
						initialData: {
							link: link,
							text: text,
							blank: blank,
						},
						buttons: [
							{
								type: 'cancel',
								text: 'Close',
							},
							{
								type: 'submit',
								text: 'Save',
								primary: true,
							},
						],
						onSubmit: function(api) {
							let data = api.getData();
							var newBtn = `<a href="${data.link}" class="btn" ${data.blank ? 'target="_blank"' : null}>
								<span class="btn__text"><span>${data.text}</span></span></a>`;

							// Pokud tlačítko existuje, tak jej před vložením smažeme
							if (btn) editor.dom.remove(editor.selection.getNode().closest('.btn'));
							editor.insertContent(newBtn);
							api.close();
						},
					});
				}
				editor.ui.registry.addToggleButton('skButtonWindowLite', {
					text: 'Tlačítko',
					tooltip: 'Tlačítko',
					onAction: skButtonWindowLite,
					onSetup: (buttonApi) => editor.selection.selectorChangedWithUnbind('a.btn', buttonApi.setActive).unbind,
				});

				// SK Quote
				// function skQuoteWindow() {
				// 	// Načti defaultní hodnoty
				// 	var quote = editor.selection.getNode().closest('.b-quote');
				// 	var image = {
				// 		value: quote ? quote.querySelector('.b-quote__img').getAttribute('src') : '/static/img/illust/noimg.svg',
				// 	};
				// 	var name = quote ? quote.querySelector('.b-quote__name')?.textContent : '';
				// 	var position = quote ? quote.querySelector('.b-quote__position')?.textContent : '';
				// 	var text = quote ? quote.querySelector('.b-quote__text')?.textContent : '';

				// 	editor.windowManager.open({
				// 		title: 'Vložit Citaci',
				// 		body: {
				// 			type: 'panel',
				// 			items: [
				// 				{
				// 					type: 'urlinput', // This is TinyMCE's native image input type
				// 					name: 'image',
				// 					filetype: 'image',
				// 					label: 'Obrázek',
				// 				},
				// 				{
				// 					type: 'input',
				// 					name: 'name',
				// 					label: 'Jméno a příjmení',
				// 				},
				// 				{
				// 					type: 'input',
				// 					name: 'position',
				// 					label: 'Pozice',
				// 				},
				// 				{
				// 					type: 'input',
				// 					name: 'text',
				// 					label: 'Text',
				// 				},
				// 			],
				// 		},
				// 		initialData: {
				// 			name: name,
				// 			position: position,
				// 			text: text,
				// 			image: image,
				// 		},
				// 		buttons: [
				// 			{
				// 				type: 'cancel',
				// 				text: 'Close',
				// 			},
				// 			{
				// 				type: 'submit',
				// 				text: 'Save',
				// 				primary: true,
				// 			},
				// 		],
				// 		onSubmit: function(api) {
				// 			let data = api.getData();

				// 			console.log(data.image.value);
				// 			var newQuote = `
				// 				<blockquote class="b-quote u-mb-last-0">
				// 					<p class="b-quote__person">
				// 						<img class="b-quote__img img img--circle" src="${data.image.value}" alt="" loading="lazy">
				// 						<span>
				// 							<span class="b-quote__name">${data.name}</span>
				// 							<span class="b-quote__position u-d-b u-c-help">${data.position}</span>
				// 						</span>
				// 						<img class="b-quote__quotes" src="/static/img/illust/quotes.svg" alt="" loading="lazy" width="40" height="40">
				// 					</p>
				// 					<p>
				// 						<i class="b-quote__text">${data.text}</i>
				// 					</p>
				// 				</blockquote>`;

				// 			// Pokud tlačítko existuje, tak jej před vložením smažeme
				// 			if (quote) editor.dom.remove(editor.selection.getNode().closest('.b-quote'));
				// 			editor.insertContent(newQuote);
				// 			api.close();
				// 		},
				// 	});
				// }
				// editor.ui.registry.addToggleButton('skQuoteWindow', {
				// 	text: 'Citace',
				// 	tooltip: 'Citace',
				// 	onAction: skQuoteWindow,
				// 	onSetup: (buttonApi) => editor.selection.selectorChangedWithUnbind('blockquote.b-quote', buttonApi.setActive).unbind,
				// });

				// SK Figure + figcaption
				// function skFigure() {
				// 	// Načti defaultní hodnoty
				// 	var quote = editor.selection.getNode().closest('figure');
				// 	var image = {
				// 		value: quote ? quote.querySelector('img').getAttribute('src') : '/static/img/illust/noimg.svg',
				// 	};
				// 	var text = quote ? quote.querySelector('figcaption')?.textContent : '';

				// 	editor.windowManager.open({
				// 		title: 'Vložit obrázek s popisem',
				// 		body: {
				// 			type: 'panel',
				// 			items: [
				// 				{
				// 					type: 'urlinput', // This is TinyMCE's native image input type
				// 					name: 'image',
				// 					filetype: 'image',
				// 					label: 'Obrázek',
				// 				},
				// 				{
				// 					type: 'input',
				// 					name: 'text',
				// 					label: 'Popisek',
				// 				},
				// 			],
				// 		},
				// 		initialData: {
				// 			text: text,
				// 			image: image,
				// 		},
				// 		buttons: [
				// 			{
				// 				type: 'cancel',
				// 				text: 'Close',
				// 			},
				// 			{
				// 				type: 'submit',
				// 				text: 'Save',
				// 				primary: true,
				// 			},
				// 		],
				// 		onSubmit: function(api) {
				// 			let data = api.getData();

				// 			var newQuote = `
				// 				<figure>
				// 					<img src="${data.image.value}" alt="" loading="lazy">
				// 					<figcaption>${data.text}</figcaption>
				// 				</figure>`;

				// 			// Pokud tlačítko existuje, tak jej před vložením smažeme
				// 			if (quote) editor.dom.remove(editor.selection.getNode().closest('figure'));
				// 			editor.insertContent(newQuote);
				// 			api.close();
				// 		},
				// 	});
				// }
				// editor.ui.registry.addToggleButton('skFigure', {
				// 	text: 'Obrázek s popisem',
				// 	tooltip: 'Obrázek s popisem',
				// 	onAction: skFigure,
				// 	onSetup: (buttonApi) => editor.selection.selectorChangedWithUnbind('figure', buttonApi.setActive).unbind,
				// });

				// SK tip
				// function skTip() {
				// 	editor.insertContent(`
				// 		<div class="b-tip tw-relative tw-rounded-xl tw-overflow-hidden tw-my-[1.6rem] md:tw-my-[2.8rem]">
				// 			<div class="b-popular__decorations b-popular__decorations--tip"></div>
				// 			<div class="b-popular__content-inner u-mb-last-0 tw-p-[2rem_2.4rem] md:tw-p-[3.2rem_12rem_3.2rem_4rem] before:tw-content-[''] before:tw-absolute before:tw-w-[0.4rem] before:tw-top-0 before:tw-bottom-0 before:tw-left-0 before:tw-bg-primary">
				// 				<img class="tw-absolute tw-top-[2rem] md:tw-top-[3.2rem] tw-right-[2rem] md:tw-right-[4rem] md:tw-w-[6rem]" src="/static/img/illust/lightbulb.svg" alt="" loading="lazy" width="40" height="40">
				// 				<p class="flag flag--yellow-gradient flag--sm tw-mb-[0.4rem]">TIP</p>
				// 				<p class="h4 u-mt-0 tw-mb-[0.8rem] tw-pr-[5rem] md:tw-pr-0">Nadpis</p>
				// 				<p>Obsah</p>
				// 			</div>
				// 		</div>`);
				// 	// editor.insertContent('' + '<a href="" class="btn">' + '<span class="btn__text">Odkaz</span>' + '</a>' + '');
				// }
				// editor.ui.registry.addButton('skTip', {
				// 	text: 'Tip',
				// 	tooltip: 'Tip',
				// 	onAction: skTip,
				// });

				// 'pre', 'p', 'code', 'h1', 'h5', 'h6'
				['h2', 'h3', 'h4', 'h5'].forEach(function(name) {
					editor.ui.registry.addButton('style-' + name, {
						tooltip: 'Toggle ' + name,
						text: name.toUpperCase(),
						onAction: function() {
							editor.execCommand('mceToggleFormat', false, name);
						},
						onPostRender: function() {
							var self = this,
								setup = function() {
									editor.formatter.formatChanged(name, function(state) {
										self.active(state);
									});
								};
							// eslint-disable-next-line @babel/no-unused-expressions
							editor.formatter ? setup() : editor.on('init', setup);
						},
					});
				});

				editor.on('blur', function() {
					editor.targetElm.dispatchEvent(new Event('blur'));
				});
			},

			valid_elements:
				'@[id|class|style|title|dir<ltr?rtl|lang|xml::lang|onclick|ondblclick|' +
				'onmousedown|onmouseup|onmouseover|onmousemove|onmouseout|onkeypress|' +
				'onkeydown|onkeyup],a[rel|rev|charset|hreflang|tabindex|accesskey|type|' +
				'name|href|target|title|class|onfocus|onblur],strong/b,em/i,strike,u,' +
				'#p,-ol[type|compact],-ul[type|compact],-li,br,img[longdesc|usemap|' +
				'src|border|alt=|title|hspace|vspace|width|height|align],-sub,-sup,' +
				'-blockquote,-table[border=0|cellspacing|cellpadding|width|frame|rules|' +
				'height|align|summary|bgcolor|background|bordercolor],-tr[rowspan|width|' +
				'height|align|valign|bgcolor|background|bordercolor],tbody,thead,tfoot,' +
				'#td[colspan|rowspan|width|height|align|valign|bgcolor|background|bordercolor' +
				'|scope],#th[colspan|rowspan|width|height|align|valign|scope],caption,-div,' +
				'-span,-code,-pre,address,-h2,-h3,-h4,-h5,-h6,hr[size|noshade],dd,dl,dt,cite,abbr,acronym,del[datetime|cite],ins[datetime|cite],' +
				'object[classid|width|height|codebase|*],param[name|value|_value],embed[type|width' +
				'|height|src|*],script[src|type],map[name],area[shape|coords|href|alt|target],bdo,' +
				'button,col[align|char|charoff|span|valign|width],colgroup[align|char|charoff|span|' +
				'valign|width],dfn,fieldset,form[action|accept|accept-charset|enctype|method],' +
				'input[accept|alt|checked|disabled|maxlength|name|readonly|size|src|type|value],' +
				'kbd,label[for],legend,noscript,optgroup[label|disabled],option[disabled|label|selected|value],' +
				'q[cite],samp,select[disabled|multiple|name|size],small,figure,figcaption,' +
				'textarea[cols|rows|disabled|name|readonly],tt,var,big,iframe[src|frameborder|allowfullscreen|width|height]',

			plugins: [
				'image wordcount link anchor table tabfocus paste media fullscreen charmap nonbreaking visualblocks lists advlist code blockquote',
			],

			template_cdate_format: '[CDATE: %m/%d/%Y : %H:%M:%S]',
			template_mdate_format: '[MDATE: %m/%d/%Y : %H:%M:%S]',

			menubar: false,

			language: 'cs',

			toolbar_mode: 'wrap',

			// for getting images from image library
			file_picker_callback: function(addCallback, value, meta) {
				if (meta.filetype === 'image') {
					// open image library
					tinyController.dispatch('openLibrary', { addCallback, variant: 'tinymce', shouldOpen: true });
				}
			},
		};

		// dafault wysiwyg
		let tinyMceDefaultConfig = tinyMceConfig;
		tinyMceDefaultConfig.formats = {
			alignleft: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'u-ta-l' },
			aligncenter: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'u-ta-c' },
			alignright: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'u-ta-r' },
		};
		tinyMceDefaultConfig.content_css = ['/static/css/wysiwyg.css', '/static/css/tailwind.css'];
		tinyMceDefaultConfig.toolbar =
			'undo redo | style-h2 style-h3 style-h4 style-h5 | bold italic | alignleft aligncenter alignright | bullist numlist outdent indent | table | link unlink anchor | image media | nonbreaking hr charmap | skButtonWindow | code';

		if (this.element.getAttribute('data-tiny-type') === 'lite') {
			tinyMceDefaultConfig.toolbar = 'undo redo | bold italic | link unlink';
		}

		// eslint-disable-next-line no-undef
		this.editor = tinymce.createEditor(this.element.id, tinyMceDefaultConfig);
		this.editor.render();
	}

	disconnect() {
		if (this.editor) {
			this.editor.destroy();
		}
	}
}
